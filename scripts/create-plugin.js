#!/usr/bin/env node

/**
 * Plugin Creation Script for NuxtJS-style Plugin System
 * 
 * Usage: node scripts/create-plugin.js <plugin-name>
 * Example: node scripts/create-plugin.js analytics
 */

const fs = require('fs');
const path = require('path');

function createPlugin(pluginName) {
  if (!pluginName) {
    console.error('❌ Plugin name is required');
    console.log('Usage: node scripts/create-plugin.js <plugin-name>');
    process.exit(1);
  }

  const pluginDir = path.join('core', 'plugins', pluginName);
  
  // Check if plugin already exists
  if (fs.existsSync(pluginDir)) {
    console.error(`❌ Plugin "${pluginName}" already exists`);
    process.exit(1);
  }

  console.log(`🚀 Creating plugin: ${pluginName}`);

  // Create directory structure
  const directories = [
    'types',
    'utils',
    'hooks',
    '__tests__'
  ];

  directories.forEach(dir => {
    const dirPath = path.join(pluginDir, dir);
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  });

  // Create index.ts files for each directory
  const indexFiles = [
    'types/index.ts',
    'utils/index.ts',
    'hooks/index.ts'
  ];

  indexFiles.forEach(file => {
    const filePath = path.join(pluginDir, file);
    fs.writeFileSync(filePath, `// ${file} exports\n// Add your exports here\n`);
    console.log(`📄 Created file: ${file}`);
  });

  // Create plugin definition file
  const pluginDefinitionContent = `// ${pluginName} plugin definition

import { definePlugin } from '@core/system/factories';
import { z } from 'zod';

// Configuration schema
const ${pluginName}ConfigSchema = z.object({
  // Add your configuration schema here
  enabled: z.boolean().default(true),
});

export const ${pluginName}Plugin = definePlugin({
  name: '${pluginName}',
  version: '1.0.0',
  dependencies: [], // Add dependencies here

  // Define hooks that this plugin provides
  hooks: {
    // Example hook
    // 'some:event': (data: any) => {
    //   // Handle the event
    // },
  },

  async setup(context) {
    const { config, addHook, callHook, events, logger, registerService } = context;
    
    // Validate configuration
    const ${pluginName}Config = ${pluginName}ConfigSchema.parse(config);
    logger.info('${pluginName.charAt(0).toUpperCase() + pluginName.slice(1)} plugin configuration validated');

    // Initialize your plugin here
    const ${pluginName}Service = {
      // Add your service methods here
    };

    // Register service if needed
    registerService('${pluginName}', ${pluginName}Service);

    // Add hooks
    addHook('example:hook', async (data) => {
      // Handle hook
      logger.debug('Example hook called with:', data);
    });

    // Listen to events
    events.on('example:event', (data) => {
      logger.debug('Example event received:', data);
    });
    
    logger.info('${pluginName.charAt(0).toUpperCase() + pluginName.slice(1)} plugin initialized successfully');
  },

  async cleanup() {
    // Add cleanup logic here
    // Remove event listeners, clear timers, etc.
  },
});
`;

  fs.writeFileSync(path.join(pluginDir, `${pluginName}-plugin.ts`), pluginDefinitionContent);
  console.log(`📄 Created ${pluginName}-plugin.ts`);

  // Create main plugin index.ts
  const mainIndexContent = `// ${pluginName} plugin exports

// Plugin definition
export { ${pluginName}Plugin } from './${pluginName}-plugin';

// Types
export * from './types';

// Utils
export * from './utils';

// Hooks
export * from './hooks';
`;

  fs.writeFileSync(path.join(pluginDir, 'index.ts'), mainIndexContent);
  console.log('📄 Created main index.ts');

  // Create README.md
  const readmeContent = `# ${pluginName.charAt(0).toUpperCase() + pluginName.slice(1)} Plugin

## Overview

Brief description of the ${pluginName} plugin's purpose and functionality.

## Features

- Feature 1
- Feature 2
- Feature 3

## Configuration

\`\`\`typescript
// In config.ts
plugins: {
  ${pluginName}: {
    enabled: true,
    config: {
      // Add configuration options here
    },
  },
}
\`\`\`

## Usage

\`\`\`typescript
// Register the plugin
import { ${pluginName}Plugin } from '@plugins/${pluginName}';
app.registerPlugin(${pluginName}Plugin);

// Use the service
const ${pluginName}Service = getService('${pluginName}');
\`\`\`

## Hooks

This plugin provides the following hooks:

- \`example:hook\` - Description of the hook

## Events

This plugin listens to the following events:

- \`example:event\` - Description of the event

## Dependencies

### Internal Dependencies
- List internal dependencies

### External Dependencies
- List external package dependencies

## Testing

Tests are located in the \`__tests__/\` directory.

Run tests:
\`\`\`bash
npm test -- ${pluginName}
\`\`\`

## Contributing

When adding new functionality:

1. Add types to \`types/index.ts\`
2. Implement functionality in the plugin definition
3. Export public API in \`index.ts\`
4. Update this README
5. Add tests
`;

  fs.writeFileSync(path.join(pluginDir, 'README.md'), readmeContent);
  console.log('📄 Created README.md');

  // Create basic types file
  const typesContent = `// ${pluginName} plugin types

// Add your type definitions here
export interface ${pluginName.charAt(0).toUpperCase() + pluginName.slice(1)}Config {
  // Configuration interface
  enabled: boolean;
}

// Add more types as needed
`;

  fs.writeFileSync(path.join(pluginDir, 'types', 'index.ts'), typesContent);
  console.log('📄 Created types/index.ts');

  console.log(`\n✅ Plugin "${pluginName}" created successfully!`);
  console.log(`\n📝 Next steps:`);
  console.log(`1. Update config.ts to add the plugin configuration`);
  console.log(`2. Register the plugin in your app initialization`);
  console.log(`3. Implement your plugin functionality`);
  console.log(`4. Add tests`);
  console.log(`5. Update the plugin's README.md`);
  console.log(`\n📍 Plugin location: ${pluginDir}`);
}

// Get plugin name from command line arguments
const pluginName = process.argv[2];
createPlugin(pluginName);
