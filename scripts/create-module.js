#!/usr/bin/env node

/**
 * Module Creation Script for Next.js Modular Architecture
 * 
 * Usage: node scripts/create-module.js <module-name>
 * Example: node scripts/create-module.js rbac
 */

const fs = require('fs');
const path = require('path');

function createModule(moduleName) {
  if (!moduleName) {
    console.error('❌ Module name is required');
    console.log('Usage: node scripts/create-module.js <module-name>');
    process.exit(1);
  }

  const moduleDir = path.join('core', 'modules', moduleName);
  
  // Check if module already exists
  if (fs.existsSync(moduleDir)) {
    console.error(`❌ Module "${moduleName}" already exists`);
    process.exit(1);
  }

  console.log(`🚀 Creating module: ${moduleName}`);

  // Create directory structure
  const directories = [
    'components',
    'components/ui',
    'components/forms',
    'components/guards',
    'hooks',
    'services',
    'utils',
    'types',
    'constants',
    'stores',
    'schemas',
    '__tests__',
    '__tests__/components',
    '__tests__/hooks',
    '__tests__/services',
    '__tests__/utils'
  ];

  directories.forEach(dir => {
    const dirPath = path.join(moduleDir, dir);
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dir}`);
  });

  // Create index.ts files for each main directory
  const indexFiles = [
    'components/index.ts',
    'hooks/index.ts',
    'services/index.ts',
    'utils/index.ts',
    'types/index.ts',
    'constants/index.ts',
    'stores/index.ts',
    'schemas/index.ts'
  ];

  indexFiles.forEach(file => {
    const filePath = path.join(moduleDir, file);
    fs.writeFileSync(filePath, `// ${file} exports\n// Add your exports here\n`);
    console.log(`📄 Created file: ${file}`);
  });

  // Create main module index.ts
  const mainIndexContent = `// ${moduleName} module exports

// Components
export * from './components';

// Hooks
export * from './hooks';

// Services
export * from './services';

// Types
export * from './types';

// Utils
export * from './utils';

// Constants
export * from './constants';

// Stores
export * from './stores';

// Schemas
export * from './schemas';
`;

  fs.writeFileSync(path.join(moduleDir, 'index.ts'), mainIndexContent);
  console.log('📄 Created main index.ts');

  // Create README.md
  const readmeContent = `# ${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)} Module

## Overview

Brief description of the ${moduleName} module's purpose and functionality.

## Features

- Feature 1
- Feature 2
- Feature 3

## Structure

\`\`\`
core/modules/${moduleName}/
├── components/         # React components
│   ├── ui/            # Pure UI components
│   ├── forms/         # Form components
│   └── guards/        # Guard/wrapper components
├── hooks/             # Custom React hooks
├── services/          # Business logic services
├── utils/             # Utility functions
├── types/             # TypeScript type definitions
├── constants/         # Module constants
├── stores/            # State management
├── schemas/           # Validation schemas
├── __tests__/         # Tests
├── index.ts           # Public API exports
└── README.md          # This file
\`\`\`

## Usage

\`\`\`typescript
import { /* exports */ } from '@/${moduleName}';
\`\`\`

## Dependencies

### Internal Dependencies
- List internal module dependencies

### External Dependencies
- List external package dependencies

## API Reference

### Components
- List exported components

### Hooks
- List exported hooks

### Services
- List exported services

### Types
- List exported types

### Utils
- List exported utilities

## Examples

\`\`\`typescript
// Add usage examples here
\`\`\`

## Testing

Tests are located in the \`__tests__/\` directory.

Run tests:
\`\`\`bash
npm test -- ${moduleName}
\`\`\`

## Contributing

When adding new functionality:

1. Add types to \`types/index.ts\`
2. Add constants to \`constants/index.ts\`
3. Implement functionality in appropriate directories
4. Export public API in \`index.ts\`
5. Update this README
6. Add tests
`;

  fs.writeFileSync(path.join(moduleDir, 'README.md'), readmeContent);
  console.log('📄 Created README.md');

  // Create basic types file
  const typesContent = `// ${moduleName} module types

// Add your type definitions here
export interface ${moduleName.charAt(0).toUpperCase() + moduleName.slice(1)}Config {
  // Configuration interface
}

// Add more types as needed
`;

  fs.writeFileSync(path.join(moduleDir, 'types', 'index.ts'), typesContent);
  console.log('📄 Created types/index.ts');

  // Create basic constants file
  const constantsContent = `// ${moduleName} module constants

// API endpoints
export const ${moduleName.toUpperCase()}_ENDPOINTS = {
  // Add endpoints here
} as const;

// Error messages
export const ${moduleName.toUpperCase()}_ERROR_MESSAGES = {
  // Add error messages here
} as const;

// Configuration
export const ${moduleName.toUpperCase()}_CONFIG = {
  // Add configuration constants here
} as const;
`;

  fs.writeFileSync(path.join(moduleDir, 'constants', 'index.ts'), constantsContent);
  console.log('📄 Created constants/index.ts');

  console.log(`\n✅ Module "${moduleName}" created successfully!`);
  console.log(`\n📝 Next steps:`);
  console.log(`1. Update tsconfig.json to add path mapping for @/${moduleName}`);
  console.log(`2. Implement your module functionality`);
  console.log(`3. Add tests`);
  console.log(`4. Update the module's README.md`);
  console.log(`\n📍 Module location: ${moduleDir}`);
}

// Get module name from command line arguments
const moduleName = process.argv[2];
createModule(moduleName);
