# NuxtJS-Style Module & Plugin System for Next.js + OpenNext

## Overview

A configuration-driven module and plugin system inspired by NuxtJS, designed for Next.js applications with OpenNext deployment. Features automatic registration, dependency resolution, and event-driven communication.

## Quick Start

### 1. Configuration (`config.ts`)
```typescript
import type { AppConfig } from './core/system/types';

export const config: AppConfig = {
  global: {
    apiUrl: process.env.NEXT_PUBLIC_API_URL,
    debug: process.env.NODE_ENV === 'development',
  },
  modules: {
    auth: { enabled: true, config: { providers: ['credentials'] } },
    database: { enabled: true, config: { provider: 'postgresql' } },
    firebase: false, // Disabled
  },
  plugins: {
    analytics: { enabled: true, config: { trackingId: 'GA_ID' } },
    seo: true, // Enabled with defaults
  },
};
```

### 2. Application Setup (`app/layout.tsx`)
```typescript
import { app } from '@core/system/app';
import config from '../config';

// Register modules and plugins
import { authModule } from '@modules/auth/auth-module';
import { databaseModule } from '@modules/database/database-module';
import { analyticsPlugin } from '@plugins/analytics/analytics-plugin';

app.setConfig(config);
app.registerModule(authModule);
app.registerModule(databaseModule);
app.registerPlugin(analyticsPlugin);

// Initialize in layout or _app
export default async function RootLayout({ children }) {
  await app.initialize();
  return <html><body>{children}</body></html>;
}
```

## Core System

### Module Definition (`defineModule`)
```typescript
import { defineModule } from '@core/system/factories';
import { z } from 'zod';

export const authModule = defineModule({
  name: 'auth',
  version: '1.0.0',
  dependencies: ['database'],
  
  async setup(context) {
    const { config, registerService, getService, events, logger } = context;
    
    // Validate config with Zod
    const authConfig = z.object({
      providers: z.array(z.string()),
      sessionDuration: z.number(),
    }).parse(config);

    // Get dependencies
    const db = getService('database');
    
    // Initialize service (better-auth example)
    const { betterAuth } = await import('better-auth');
    const auth = betterAuth({
      database: db,
      emailAndPassword: { enabled: true },
    });

    // Create service
    const authService = {
      signIn: auth.signIn,
      signOut: auth.signOut,
      getSession: auth.getSession,
    };

    // Register service
    registerService('auth', authService);
    
    // Emit events
    events.emit('auth:initialized', { config: authConfig });
    
    logger.info('Auth module initialized');
  },
});
```

### Plugin Definition (`definePlugin`)
```typescript
import { definePlugin } from '@core/system/factories';

export const analyticsPlugin = definePlugin({
  name: 'analytics',
  version: '1.0.0',
  dependencies: ['auth'], // Optional dependency
  
  hooks: {
    'auth:login': (user) => {
      // Track login event
      gtag('event', 'login', { user_id: user.id });
    },
  },
  
  async setup(context) {
    const { config, addHook, events, logger } = context;
    
    // Initialize analytics
    const { gtag } = await import('gtag');
    
    // Add hooks
    addHook('page:view', (url) => {
      gtag('config', config.trackingId, { page_path: url });
    });
    
    // Listen to events
    events.on('auth:signup', (user) => {
      gtag('event', 'sign_up', { user_id: user.id });
    });
    
    logger.info('Analytics plugin initialized');
  },
});
```

## Technology Stack Integration

### Database Module (Drizzle ORM)
```typescript
export const databaseModule = defineModule({
  name: 'database',
  version: '1.0.0',
  
  async setup(context) {
    const { drizzle } = await import('drizzle-orm/postgres-js');
    const postgres = await import('postgres');
    
    const client = postgres(context.config.url);
    const db = drizzle(client);
    
    context.registerService('database', db);
    context.events.emit('db:connected', {});
  },
});
```

### State Management (Redux Toolkit)
```typescript
export const storeModule = defineModule({
  name: 'store',
  version: '1.0.0',
  
  async setup(context) {
    const { configureStore, createApi } = await import('@reduxjs/toolkit');
    const { fetchBaseQuery } = await import('@reduxjs/toolkit/query');
    
    // Create API slice
    const api = createApi({
      baseQuery: fetchBaseQuery({ baseUrl: '/api' }),
      endpoints: (builder) => ({}),
    });
    
    // Create store
    const store = configureStore({
      reducer: { api: api.reducer },
      middleware: (getDefaultMiddleware) =>
        getDefaultMiddleware().concat(api.middleware),
    });
    
    context.registerService('store', store);
    context.registerService('api', api);
  },
});
```

## Event System

### Event Communication
```typescript
// In any module/plugin
events.emit('user:created', { user });
events.on('user:created', (data) => {
  logger.info('User created:', data.user.email);
});

// Async events
const results = await events.emitAsync('user:validate', user);
```

### Hook System
```typescript
// Add hooks in plugins
addHook('auth:beforeLogin', async (credentials) => {
  // Validate or modify credentials
  return { ...credentials, timestamp: Date.now() };
});

// Call hooks in modules
const results = await callHook('auth:beforeLogin', credentials);
```

## Service Registry

### Register Services
```typescript
// In module setup
registerService('auth', authService);
registerService('db', database);
```

### Use Services
```typescript
// In components or other modules
const auth = getService('auth');
const user = await auth.getUser();

// In API routes
import { registry } from '@core/system/registry';
const db = registry.getService('database');
```

## OpenNext Compatibility

### Web APIs Only
```typescript
// ✅ Use Web APIs
const id = crypto.randomUUID();
const response = await fetch('/api/data');

// ❌ Avoid Node.js APIs
import fs from 'fs'; // Don't use in client code
```

### Edge Runtime Support
```typescript
// API route with edge runtime
export const runtime = 'edge';

export async function GET() {
  const auth = registry.getService('auth');
  const user = await auth.getUser();
  return Response.json({ user });
}
```

## Module Structure

```
core/modules/{module-name}/
├── types/              # TypeScript types
├── schemas/            # Zod validation schemas
├── services/           # Business logic
├── components/         # React components
├── hooks/              # React hooks
├── {module-name}-module.ts  # Module definition
└── index.ts            # Public exports
```

## Configuration Examples

### Environment-Based Config
```typescript
export const config: AppConfig = {
  modules: {
    auth: {
      enabled: true,
      config: {
        providers: process.env.NODE_ENV === 'production' 
          ? ['credentials', 'google'] 
          : ['credentials'],
      },
    },
    firebase: {
      enabled: !!process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      config: {
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      },
    },
  },
  plugins: {
    analytics: process.env.NODE_ENV === 'production',
    devtools: process.env.NODE_ENV === 'development',
  },
};
```

### Feature Flags
```typescript
modules: {
  rbac: process.env.FEATURE_RBAC === 'true',
  notifications: {
    enabled: process.env.FEATURE_NOTIFICATIONS === 'true',
    config: {
      providers: ['email', 'push'],
    },
  },
},
```

## Best Practices

### 1. Module Design
- Single responsibility per module
- Clear dependencies
- Validate configuration with Zod
- Use Web APIs only

### 2. Error Handling
```typescript
try {
  await module.setup(context);
} catch (error) {
  logger.error(`Module ${module.name} failed:`, error);
  throw error;
}
```

### 3. Type Safety
```typescript
// Define typed events
interface AppEvents {
  'user:created': { user: User };
  'auth:login': { user: User; session: Session };
}

// Use typed service registry
const auth = getService<AuthService>('auth');
```

### 4. Performance
- Lazy load heavy dependencies
- Use dynamic imports
- Implement cleanup methods

## Migration from Existing Code

### 1. Extract to Modules
```typescript
// Before: Direct imports
import { authService } from '../services/auth';

// After: Service registry
const auth = getService('auth');
```

### 2. Configuration-Driven
```typescript
// Before: Hard-coded features
const enableAnalytics = true;

// After: Config-driven
const analyticsEnabled = config.plugins.analytics;
```

### 3. Event-Driven Communication
```typescript
// Before: Direct coupling
userService.onCreate((user) => {
  emailService.sendWelcome(user);
  analyticsService.track('signup', user);
});

// After: Event-driven
events.emit('user:created', { user });
```

This system provides NuxtJS-like developer experience with automatic module loading, dependency resolution, and configuration-driven architecture optimized for Next.js and OpenNext deployment.
