// Core system exports

// Main application system
export { app } from './app';

// Factory functions
export { defineModule, definePlugin } from './factories';

// Event system
export { globalEvents, SystemEventEmitter } from './event-emitter';

// Service registry
export { registry } from './registry';

// Logger
export { logger, SystemLogger, LOG_LEVELS } from './logger';

// Types
export type {
  ModuleDefinition,
  PluginDefinition,
  ModuleContext,
  PluginContext,
  EventEmitter,
  Logger,
  AppConfig,
  ModuleConfig,
  PluginConfig,
  SystemEvents,
  ServiceRegistry,
  HookRegistry,
} from './types';

// Utilities
export {
  createModuleContext,
  createPluginContext,
  validateDependencies,
  sortByDependencies,
} from './factories';
