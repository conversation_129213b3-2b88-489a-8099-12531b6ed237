// Core system types for module and plugin definitions

export interface ModuleDefinition {
  name: string;
  version: string;
  dependencies?: string[];
  setup: (context: ModuleContext) => Promise<void> | void;
  cleanup?: () => Promise<void> | void;
  config?: Record<string, any>;
}

export interface PluginDefinition {
  name: string;
  version: string;
  dependencies?: string[];
  setup: (context: PluginContext) => Promise<void> | void;
  cleanup?: () => Promise<void> | void;
  config?: Record<string, any>;
  hooks?: Record<string, Function>;
}

export interface ModuleContext {
  config: any;
  events: EventEmitter;
  registerService: (name: string, service: any) => void;
  getService: <T = any>(name: string) => T | undefined;
  logger: Logger;
}

export interface PluginContext extends ModuleContext {
  addHook: (name: string, handler: Function) => void;
  callHook: (name: string, ...args: any[]) => Promise<any[]>;
}

export interface EventEmitter {
  on: (event: string, handler: Function) => void;
  off: (event: string, handler: Function) => void;
  emit: (event: string, ...args: any[]) => void;
  once: (event: string, handler: Function) => void;
}

export interface Logger {
  info: (message: string, ...args: any[]) => void;
  warn: (message: string, ...args: any[]) => void;
  error: (message: string, ...args: any[]) => void;
  debug: (message: string, ...args: any[]) => void;
}

export interface AppConfig {
  modules: {
    [key: string]: boolean | ModuleConfig;
  };
  plugins: {
    [key: string]: boolean | PluginConfig;
  };
  global?: {
    apiUrl?: string;
    debug?: boolean;
    [key: string]: any;
  };
}

export interface ModuleConfig {
  enabled: boolean;
  config?: Record<string, any>;
}

export interface PluginConfig {
  enabled: boolean;
  config?: Record<string, any>;
}

// Event types for type safety
export interface SystemEvents {
  'module:registered': { name: string; module: ModuleDefinition };
  'module:initialized': { name: string };
  'plugin:registered': { name: string; plugin: PluginDefinition };
  'plugin:initialized': { name: string };
  'app:ready': {};
  'auth:login': { user: any };
  'auth:logout': {};
  'db:connected': {};
  'db:error': { error: Error };
}

// Service registry types
export interface ServiceRegistry {
  auth?: any;
  db?: any;
  firebase?: any;
  api?: any;
  [key: string]: any;
}

// Hook system types
export interface HookRegistry {
  'app:created': Function[];
  'auth:beforeLogin': Function[];
  'auth:afterLogin': Function[];
  'db:beforeQuery': Function[];
  'db:afterQuery': Function[];
  [key: string]: Function[];
}
