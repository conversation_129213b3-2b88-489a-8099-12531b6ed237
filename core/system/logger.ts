// Web API compatible logger for modules and plugins

export interface LogLevel {
  DEBUG: 0;
  INFO: 1;
  WARN: 2;
  ERROR: 3;
}

export const LOG_LEVELS: LogLevel = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
} as const;

export class SystemLogger {
  private level: number = LOG_LEVELS.INFO;
  private prefix: string = '';

  constructor(prefix?: string, level?: number) {
    this.prefix = prefix ? `[${prefix}]` : '';
    this.level = level ?? LOG_LEVELS.INFO;
  }

  setLevel(level: number): void {
    this.level = level;
  }

  debug(message: string, ...args: any[]): void {
    if (this.level <= LOG_LEVELS.DEBUG) {
      console.debug(`${this.prefix} ${message}`, ...args);
    }
  }

  info(message: string, ...args: any[]): void {
    if (this.level <= LOG_LEVELS.INFO) {
      console.info(`${this.prefix} ${message}`, ...args);
    }
  }

  warn(message: string, ...args: any[]): void {
    if (this.level <= LOG_LEVELS.WARN) {
      console.warn(`${this.prefix} ${message}`, ...args);
    }
  }

  error(message: string, ...args: any[]): void {
    if (this.level <= LOG_LEVELS.ERROR) {
      console.error(`${this.prefix} ${message}`, ...args);
    }
  }

  child(prefix: string): SystemLogger {
    const childPrefix = this.prefix ? `${this.prefix}:${prefix}` : prefix;
    return new SystemLogger(childPrefix, this.level);
  }
}

// Global logger instance
export const logger = new SystemLogger('SYSTEM');
