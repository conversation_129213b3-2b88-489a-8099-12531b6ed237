// Service and hook registry for module/plugin system

import { globalEvents } from './event-emitter';
import { logger } from './logger';
import type { ServiceRegistry, HookRegistry } from './types';

class SystemRegistry {
  private services: Map<string, any> = new Map();
  private hooks: Map<string, Set<Function>> = new Map();

  // Service management
  registerService(name: string, service: any): void {
    if (this.services.has(name)) {
      logger.warn(`Service ${name} is already registered, overwriting`);
    }
    
    this.services.set(name, service);
    globalEvents.emit('service:registered', { name, service });
    logger.debug(`Service registered: ${name}`);
  }

  getService<T = any>(name: string): T | undefined {
    return this.services.get(name) as T;
  }

  hasService(name: string): boolean {
    return this.services.has(name);
  }

  removeService(name: string): boolean {
    const removed = this.services.delete(name);
    if (removed) {
      globalEvents.emit('service:removed', { name });
      logger.debug(`Service removed: ${name}`);
    }
    return removed;
  }

  getAllServices(): ServiceRegistry {
    const services: ServiceRegistry = {};
    this.services.forEach((service, name) => {
      services[name] = service;
    });
    return services;
  }

  // Hook management
  addHook(name: string, handler: Function): void {
    if (!this.hooks.has(name)) {
      this.hooks.set(name, new Set());
    }
    
    this.hooks.get(name)!.add(handler);
    logger.debug(`Hook added: ${name}`);
  }

  removeHook(name: string, handler: Function): boolean {
    const handlers = this.hooks.get(name);
    if (handlers) {
      const removed = handlers.delete(handler);
      if (handlers.size === 0) {
        this.hooks.delete(name);
      }
      if (removed) {
        logger.debug(`Hook removed: ${name}`);
      }
      return removed;
    }
    return false;
  }

  async callHook(name: string, ...args: any[]): Promise<any[]> {
    const handlers = this.hooks.get(name);
    if (!handlers || handlers.size === 0) {
      return [];
    }

    const results: any[] = [];
    
    for (const handler of handlers) {
      try {
        const result = await handler(...args);
        results.push(result);
      } catch (error) {
        logger.error(`Error in hook ${name}:`, error);
        results.push(error);
      }
    }

    return results;
  }

  getHooks(name: string): Function[] {
    const handlers = this.hooks.get(name);
    return handlers ? Array.from(handlers) : [];
  }

  getAllHooks(): HookRegistry {
    const hooks: HookRegistry = {};
    this.hooks.forEach((handlers, name) => {
      hooks[name] = Array.from(handlers);
    });
    return hooks;
  }

  // Cleanup
  clear(): void {
    this.services.clear();
    this.hooks.clear();
    logger.debug('Registry cleared');
  }

  // Debug information
  getStats(): {
    services: number;
    hooks: number;
    serviceNames: string[];
    hookNames: string[];
  } {
    return {
      services: this.services.size,
      hooks: this.hooks.size,
      serviceNames: Array.from(this.services.keys()),
      hookNames: Array.from(this.hooks.keys()),
    };
  }
}

// Global registry instance
export const registry = new SystemRegistry();
