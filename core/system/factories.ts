// Factory functions for defining modules and plugins

import type { ModuleDefinition, PluginDefinition, ModuleContext, PluginContext } from './types';
import { globalEvents } from './event-emitter';
import { logger } from './logger';
import { registry } from './registry';

// Module factory function
export function defineModule(definition: Omit<ModuleDefinition, 'setup'> & {
  setup: (context: ModuleContext) => Promise<void> | void;
}): ModuleDefinition {
  // Validate module definition
  if (!definition.name) {
    throw new Error('Module name is required');
  }
  
  if (!definition.version) {
    throw new Error('Module version is required');
  }

  if (typeof definition.setup !== 'function') {
    throw new Error('Module setup function is required');
  }

  return {
    name: definition.name,
    version: definition.version,
    dependencies: definition.dependencies || [],
    config: definition.config || {},
    setup: definition.setup,
    cleanup: definition.cleanup,
  };
}

// Plugin factory function
export function definePlugin(definition: Omit<PluginDefinition, 'setup'> & {
  setup: (context: PluginContext) => Promise<void> | void;
}): PluginDefinition {
  // Validate plugin definition
  if (!definition.name) {
    throw new Error('Plugin name is required');
  }
  
  if (!definition.version) {
    throw new Error('Plugin version is required');
  }

  if (typeof definition.setup !== 'function') {
    throw new Error('Plugin setup function is required');
  }

  return {
    name: definition.name,
    version: definition.version,
    dependencies: definition.dependencies || [],
    config: definition.config || {},
    hooks: definition.hooks || {},
    setup: definition.setup,
    cleanup: definition.cleanup,
  };
}

// Create module context
export function createModuleContext(config: any): ModuleContext {
  const moduleLogger = logger.child('MODULE');
  
  return {
    config,
    events: globalEvents,
    registerService: (name: string, service: any) => {
      registry.registerService(name, service);
    },
    getService: <T = any>(name: string): T | undefined => {
      return registry.getService<T>(name);
    },
    logger: moduleLogger,
  };
}

// Create plugin context
export function createPluginContext(config: any): PluginContext {
  const pluginLogger = logger.child('PLUGIN');
  
  return {
    config,
    events: globalEvents,
    registerService: (name: string, service: any) => {
      registry.registerService(name, service);
    },
    getService: <T = any>(name: string): T | undefined => {
      return registry.getService<T>(name);
    },
    addHook: (name: string, handler: Function) => {
      registry.addHook(name, handler);
    },
    callHook: async (name: string, ...args: any[]): Promise<any[]> => {
      return registry.callHook(name, ...args);
    },
    logger: pluginLogger,
  };
}

// Utility function to validate dependencies
export function validateDependencies(
  name: string,
  dependencies: string[],
  availableModules: Set<string>
): void {
  const missing = dependencies.filter(dep => !availableModules.has(dep));
  
  if (missing.length > 0) {
    throw new Error(
      `Module/Plugin ${name} has missing dependencies: ${missing.join(', ')}`
    );
  }
}

// Utility function to sort modules/plugins by dependencies
export function sortByDependencies<T extends { name: string; dependencies?: string[] }>(
  items: T[]
): T[] {
  const sorted: T[] = [];
  const visited = new Set<string>();
  const visiting = new Set<string>();

  function visit(item: T) {
    if (visiting.has(item.name)) {
      throw new Error(`Circular dependency detected involving ${item.name}`);
    }
    
    if (visited.has(item.name)) {
      return;
    }

    visiting.add(item.name);

    // Visit dependencies first
    if (item.dependencies) {
      for (const depName of item.dependencies) {
        const dep = items.find(i => i.name === depName);
        if (dep) {
          visit(dep);
        }
      }
    }

    visiting.delete(item.name);
    visited.add(item.name);
    sorted.push(item);
  }

  for (const item of items) {
    if (!visited.has(item.name)) {
      visit(item);
    }
  }

  return sorted;
}
