// Main application system for module and plugin management

import type { AppConfig, ModuleDefinition, PluginDefinition } from './types';
import { globalEvents } from './event-emitter';
import { logger } from './logger';
import { registry } from './registry';
import { 
  createModuleContext, 
  createPluginContext, 
  validateDependencies, 
  sortByDependencies 
} from './factories';

class AppSystem {
  private modules: Map<string, ModuleDefinition> = new Map();
  private plugins: Map<string, PluginDefinition> = new Map();
  private initialized = false;
  private config: AppConfig = { modules: {}, plugins: {} };

  // Set application configuration
  setConfig(config: AppConfig): void {
    this.config = config;
    
    // Set global debug level
    if (config.global?.debug) {
      logger.setLevel(0); // DEBUG level
    }
    
    logger.info('App configuration set');
  }

  // Register a module
  registerModule(module: ModuleDefinition): void {
    if (this.initialized) {
      throw new Error('Cannot register modules after initialization');
    }

    this.modules.set(module.name, module);
    globalEvents.emit('module:registered', { name: module.name, module });
    logger.debug(`Module registered: ${module.name} v${module.version}`);
  }

  // Register a plugin
  registerPlugin(plugin: PluginDefinition): void {
    if (this.initialized) {
      throw new Error('Cannot register plugins after initialization');
    }

    this.plugins.set(plugin.name, plugin);
    globalEvents.emit('plugin:registered', { name: plugin.name, plugin });
    logger.debug(`Plugin registered: ${plugin.name} v${plugin.version}`);
  }

  // Initialize the application
  async initialize(): Promise<void> {
    if (this.initialized) {
      logger.warn('App already initialized');
      return;
    }

    logger.info('Initializing application...');

    try {
      // Initialize modules first
      await this.initializeModules();
      
      // Then initialize plugins
      await this.initializePlugins();

      this.initialized = true;
      globalEvents.emit('app:ready', {});
      logger.info('Application initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize application:', error);
      throw error;
    }
  }

  // Initialize modules
  private async initializeModules(): Promise<void> {
    const enabledModules = this.getEnabledModules();
    const sortedModules = sortByDependencies(enabledModules);

    logger.info(`Initializing ${sortedModules.length} modules...`);

    for (const module of sortedModules) {
      await this.initializeModule(module);
    }
  }

  // Initialize a single module
  private async initializeModule(module: ModuleDefinition): Promise<void> {
    try {
      // Validate dependencies
      const availableModules = new Set(this.getEnabledModules().map(m => m.name));
      validateDependencies(module.name, module.dependencies || [], availableModules);

      // Get module configuration
      const moduleConfig = this.getModuleConfig(module.name);
      
      // Create context and initialize
      const context = createModuleContext(moduleConfig);
      await module.setup(context);

      globalEvents.emit('module:initialized', { name: module.name });
      logger.info(`Module initialized: ${module.name}`);
    } catch (error) {
      logger.error(`Failed to initialize module ${module.name}:`, error);
      throw error;
    }
  }

  // Initialize plugins
  private async initializePlugins(): Promise<void> {
    const enabledPlugins = this.getEnabledPlugins();
    const sortedPlugins = sortByDependencies(enabledPlugins);

    logger.info(`Initializing ${sortedPlugins.length} plugins...`);

    for (const plugin of sortedPlugins) {
      await this.initializePlugin(plugin);
    }
  }

  // Initialize a single plugin
  private async initializePlugin(plugin: PluginDefinition): Promise<void> {
    try {
      // Validate dependencies
      const availableItems = new Set([
        ...this.getEnabledModules().map(m => m.name),
        ...this.getEnabledPlugins().map(p => p.name)
      ]);
      validateDependencies(plugin.name, plugin.dependencies || [], availableItems);

      // Get plugin configuration
      const pluginConfig = this.getPluginConfig(plugin.name);
      
      // Create context and initialize
      const context = createPluginContext(pluginConfig);
      
      // Register plugin hooks
      if (plugin.hooks) {
        Object.entries(plugin.hooks).forEach(([hookName, handler]) => {
          context.addHook(hookName, handler);
        });
      }

      await plugin.setup(context);

      globalEvents.emit('plugin:initialized', { name: plugin.name });
      logger.info(`Plugin initialized: ${plugin.name}`);
    } catch (error) {
      logger.error(`Failed to initialize plugin ${plugin.name}:`, error);
      throw error;
    }
  }

  // Get enabled modules
  private getEnabledModules(): ModuleDefinition[] {
    return Array.from(this.modules.values()).filter(module => {
      const config = this.config.modules[module.name];
      return config === true || (typeof config === 'object' && config.enabled);
    });
  }

  // Get enabled plugins
  private getEnabledPlugins(): PluginDefinition[] {
    return Array.from(this.plugins.values()).filter(plugin => {
      const config = this.config.plugins[plugin.name];
      return config === true || (typeof config === 'object' && config.enabled);
    });
  }

  // Get module configuration
  private getModuleConfig(name: string): any {
    const config = this.config.modules[name];
    const globalConfig = this.config.global || {};
    
    if (typeof config === 'object' && config.config) {
      return { ...globalConfig, ...config.config };
    }
    
    return globalConfig;
  }

  // Get plugin configuration
  private getPluginConfig(name: string): any {
    const config = this.config.plugins[name];
    const globalConfig = this.config.global || {};
    
    if (typeof config === 'object' && config.config) {
      return { ...globalConfig, ...config.config };
    }
    
    return globalConfig;
  }

  // Cleanup
  async cleanup(): Promise<void> {
    logger.info('Cleaning up application...');

    // Cleanup plugins first (reverse order)
    const enabledPlugins = this.getEnabledPlugins().reverse();
    for (const plugin of enabledPlugins) {
      if (plugin.cleanup) {
        try {
          await plugin.cleanup();
          logger.debug(`Plugin cleaned up: ${plugin.name}`);
        } catch (error) {
          logger.error(`Failed to cleanup plugin ${plugin.name}:`, error);
        }
      }
    }

    // Then cleanup modules (reverse order)
    const enabledModules = this.getEnabledModules().reverse();
    for (const module of enabledModules) {
      if (module.cleanup) {
        try {
          await module.cleanup();
          logger.debug(`Module cleaned up: ${module.name}`);
        } catch (error) {
          logger.error(`Failed to cleanup module ${module.name}:`, error);
        }
      }
    }

    // Clear registry
    registry.clear();
    
    this.initialized = false;
    logger.info('Application cleanup completed');
  }

  // Get system status
  getStatus() {
    return {
      initialized: this.initialized,
      modules: {
        registered: this.modules.size,
        enabled: this.getEnabledModules().length,
      },
      plugins: {
        registered: this.plugins.size,
        enabled: this.getEnabledPlugins().length,
      },
      registry: registry.getStats(),
    };
  }
}

// Global app instance
export const app = new AppSystem();
