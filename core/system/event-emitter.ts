// Web API compatible event emitter for module/plugin communication

export class SystemEventEmitter {
  private events: Map<string, Set<Function>> = new Map();
  private onceEvents: Map<string, Set<Function>> = new Map();

  on(event: string, handler: Function): void {
    if (!this.events.has(event)) {
      this.events.set(event, new Set());
    }
    this.events.get(event)!.add(handler);
  }

  off(event: string, handler: Function): void {
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.events.delete(event);
      }
    }
  }

  once(event: string, handler: Function): void {
    if (!this.onceEvents.has(event)) {
      this.onceEvents.set(event, new Set());
    }
    this.onceEvents.get(event)!.add(handler);
  }

  emit(event: string, ...args: any[]): void {
    // Handle regular events
    const handlers = this.events.get(event);
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }

    // Handle once events
    const onceHandlers = this.onceEvents.get(event);
    if (onceHandlers) {
      onceHandlers.forEach(handler => {
        try {
          handler(...args);
        } catch (error) {
          console.error(`Error in once event handler for ${event}:`, error);
        }
      });
      this.onceEvents.delete(event);
    }
  }

  async emitAsync(event: string, ...args: any[]): Promise<any[]> {
    const results: any[] = [];
    
    // Handle regular events
    const handlers = this.events.get(event);
    if (handlers) {
      for (const handler of handlers) {
        try {
          const result = await handler(...args);
          results.push(result);
        } catch (error) {
          console.error(`Error in async event handler for ${event}:`, error);
          results.push(error);
        }
      }
    }

    // Handle once events
    const onceHandlers = this.onceEvents.get(event);
    if (onceHandlers) {
      for (const handler of onceHandlers) {
        try {
          const result = await handler(...args);
          results.push(result);
        } catch (error) {
          console.error(`Error in async once event handler for ${event}:`, error);
          results.push(error);
        }
      }
      this.onceEvents.delete(event);
    }

    return results;
  }

  removeAllListeners(event?: string): void {
    if (event) {
      this.events.delete(event);
      this.onceEvents.delete(event);
    } else {
      this.events.clear();
      this.onceEvents.clear();
    }
  }

  listenerCount(event: string): number {
    const regular = this.events.get(event)?.size || 0;
    const once = this.onceEvents.get(event)?.size || 0;
    return regular + once;
  }

  eventNames(): string[] {
    const names = new Set<string>();
    this.events.keys().forEach(name => names.add(name));
    this.onceEvents.keys().forEach(name => names.add(name));
    return Array.from(names);
  }
}

// Global event emitter instance
export const globalEvents = new SystemEventEmitter();
