// Analytics plugin for tracking user events

import { definePlugin } from '@core/system/factories';
import { z } from 'zod';

// Configuration schema
const analyticsConfigSchema = z.object({
  trackingId: z.string(),
  anonymizeIp: z.boolean().optional().default(true),
  respectDnt: z.boolean().optional().default(true),
  debug: z.boolean().optional().default(false),
});

export const analyticsPlugin = definePlugin({
  name: 'analytics',
  version: '1.0.0',
  dependencies: [], // Optional: could depend on auth module

  // Define hooks that this plugin provides
  hooks: {
    // Hook into auth events
    'auth:login': (data: { user: any; session: any }) => {
      // This will be called when auth:login event is emitted
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'login', {
          user_id: data.user.id,
          method: 'email',
        });
      }
    },

    'auth:signup': (data: { user: any; session: any }) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'sign_up', {
          user_id: data.user.id,
          method: 'email',
        });
      }
    },

    'auth:logout': (data: { userId: string }) => {
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'logout', {
          user_id: data.userId,
        });
      }
    },
  },

  async setup(context) {
    const { config, addHook, events, logger } = context;
    
    // Validate configuration
    const analyticsConfig = analyticsConfigSchema.parse(config);
    logger.info('Analytics plugin configuration validated');

    // Check if we're in browser environment
    if (typeof window === 'undefined') {
      logger.info('Analytics plugin: Server-side setup, skipping client initialization');
      return;
    }

    // Check Do Not Track
    if (analyticsConfig.respectDnt && navigator.doNotTrack === '1') {
      logger.info('Analytics plugin: Do Not Track enabled, skipping initialization');
      return;
    }

    try {
      // Load Google Analytics
      const script = document.createElement('script');
      script.async = true;
      script.src = `https://www.googletagmanager.com/gtag/js?id=${analyticsConfig.trackingId}`;
      document.head.appendChild(script);

      // Initialize gtag
      window.dataLayer = window.dataLayer || [];
      function gtag(...args: any[]) {
        window.dataLayer.push(args);
      }
      window.gtag = gtag;

      gtag('js', new Date());
      gtag('config', analyticsConfig.trackingId, {
        anonymize_ip: analyticsConfig.anonymizeIp,
        debug_mode: analyticsConfig.debug,
      });

      // Create analytics service
      const analyticsService = {
        // Track page view
        trackPageView(url: string, title?: string) {
          if (window.gtag) {
            window.gtag('config', analyticsConfig.trackingId, {
              page_path: url,
              page_title: title,
            });
          }
        },

        // Track custom event
        trackEvent(eventName: string, parameters?: Record<string, any>) {
          if (window.gtag) {
            window.gtag('event', eventName, parameters);
          }
        },

        // Track user properties
        setUserProperties(properties: Record<string, any>) {
          if (window.gtag) {
            window.gtag('config', analyticsConfig.trackingId, {
              custom_map: properties,
            });
          }
        },

        // Track conversion
        trackConversion(conversionId: string, value?: number, currency?: string) {
          if (window.gtag) {
            window.gtag('event', 'conversion', {
              send_to: conversionId,
              value: value,
              currency: currency || 'USD',
            });
          }
        },

        // Enable/disable analytics
        setEnabled(enabled: boolean) {
          if (window.gtag) {
            window.gtag('consent', 'update', {
              analytics_storage: enabled ? 'granted' : 'denied',
            });
          }
        },
      };

      // Register analytics service
      context.registerService('analytics', analyticsService);

      // Add hooks for common events
      addHook('page:view', (url: string, title?: string) => {
        analyticsService.trackPageView(url, title);
      });

      addHook('user:action', (action: string, data?: Record<string, any>) => {
        analyticsService.trackEvent(action, data);
      });

      // Listen to navigation events (Next.js router)
      if (typeof window !== 'undefined') {
        // Track initial page view
        analyticsService.trackPageView(window.location.pathname);

        // Listen to route changes
        events.on('route:change', ({ url }: { url: string }) => {
          analyticsService.trackPageView(url);
        });
      }

      // Listen to auth events
      events.on('auth:login', (data) => {
        analyticsService.trackEvent('login', {
          user_id: data.user.id,
          method: 'email',
        });
      });

      events.on('auth:signup', (data) => {
        analyticsService.trackEvent('sign_up', {
          user_id: data.user.id,
          method: 'email',
        });
      });

      events.on('auth:logout', (data) => {
        analyticsService.trackEvent('logout', {
          user_id: data.userId,
        });
      });

      logger.info('Analytics plugin initialized successfully');
    } catch (error) {
      logger.error('Analytics plugin initialization failed:', error);
      throw error;
    }
  },

  async cleanup() {
    // Remove analytics scripts and clean up
    if (typeof window !== 'undefined') {
      const scripts = document.querySelectorAll('script[src*="googletagmanager.com"]');
      scripts.forEach(script => script.remove());
      
      // Clear gtag
      delete window.gtag;
      delete window.dataLayer;
    }
  },
});

// Extend window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void;
    dataLayer: any[];
  }
}
