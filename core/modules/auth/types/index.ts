// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  createdAt: Date;
  updatedAt: Date;
}

// Authentication state
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Credentials for login
export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

// Data for user signup
export interface SignupData {
  email: string;
  password: string;
  name: string;
  confirmPassword: string;
}

// Session data
export interface Session {
  user: User;
  token: string;
  refreshToken?: string;
  expiresAt: Date;
}

// Token payload
export interface TokenPayload {
  userId: string;
  email: string;
  role: string;
  iat: number;
  exp: number;
}

// Password reset types
export interface PasswordResetRequest {
  email: string;
}

export interface PasswordReset {
  token: string;
  password: string;
  confirmPassword: string;
}

// Auth error types
export class AuthError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'AuthError';
  }
}

export class ValidationError extends Error {
  constructor(
    message: string,
    public field: string
  ) {
    super(message);
    this.name = 'ValidationError';
  }
}

// Auth events
export type AuthEvent = 
  | { type: 'LOGIN'; payload: User }
  | { type: 'LOGOUT'; payload: null }
  | { type: 'SIGNUP'; payload: User }
  | { type: 'SESSION_EXPIRED'; payload: null }
  | { type: 'PASSWORD_RESET'; payload: { email: string } };
