// Better-auth compatible types

export interface User {
  id: string;
  email: string;
  name: string;
  image?: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: Date;
  token: string;
  ipAddress?: string;
  userAgent?: string;
}

export interface Account {
  id: string;
  userId: string;
  accountId: string;
  providerId: string;
  accessToken?: string;
  refreshToken?: string;
  idToken?: string;
  accessTokenExpiresAt?: Date;
  refreshTokenExpiresAt?: Date;
  scope?: string;
  password?: string;
  salt?: string;
}

// Auth configuration
export interface AuthConfig {
  providers: AuthProvider[];
  sessionDuration: number;
  cookieName: string;
  redirects: {
    login: string;
    logout: string;
    afterLogin: string;
    afterLogout: string;
  };
}

export interface AuthProvider {
  id: string;
  name: string;
  type: 'oauth' | 'credentials';
  config?: Record<string, any>;
}

// Auth state for Redux
export interface AuthState {
  user: User | null;
  session: Session | null;
  isLoading: boolean;
  error: string | null;
  isAuthenticated: boolean;
}

// API types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignupRequest {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  user: User;
  session: Session;
}

// Error types
export class AuthError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode: number = 400
  ) {
    super(message);
    this.name = 'AuthError';
  }
}

// Events
export interface AuthEvents {
  'auth:login': { user: User; session: Session };
  'auth:logout': { userId: string };
  'auth:signup': { user: User; session: Session };
  'auth:session-expired': { userId: string };
  'auth:password-reset': { email: string };
  'auth:email-verified': { userId: string };
}
