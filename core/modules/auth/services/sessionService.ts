import { Session, User } from '../types';
import { STORAGE_KEYS, COOKIE_NAMES, TOKEN_CONFIG } from '../constants';
import { storage, isTokenExpired } from '../utils';

export class SessionService {
  private session: Session | null = null;

  // Get current session
  async getSession(): Promise<Session | null> {
    if (this.session) {
      return this.session;
    }

    // Try to restore session from storage
    const storedSession = this.getStoredSession();
    if (storedSession && !this.isSessionExpired(storedSession)) {
      this.session = storedSession;
      return storedSession;
    }

    return null;
  }

  // Set new session
  async setSession(sessionData: { user: User; token: string; refreshToken?: string }): Promise<void> {
    const expiresAt = new Date(Date.now() + TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY);
    
    const session: Session = {
      user: sessionData.user,
      token: sessionData.token,
      refreshToken: sessionData.refreshToken,
      expiresAt,
    };

    this.session = session;
    this.storeSession(session);
  }

  // Clear session
  async clearSession(): Promise<void> {
    this.session = null;
    this.removeStoredSession();
  }

  // Check if session is valid
  async isValidSession(): Promise<boolean> {
    const session = await this.getSession();
    return session !== null && !this.isSessionExpired(session);
  }

  // Get current user from session
  async getCurrentUser(): Promise<User | null> {
    const session = await this.getSession();
    return session?.user || null;
  }

  // Get access token
  async getAccessToken(): Promise<string | null> {
    const session = await this.getSession();
    return session?.token || null;
  }

  // Get refresh token
  async getRefreshToken(): Promise<string | null> {
    const session = await this.getSession();
    return session?.refreshToken || null;
  }

  // Update user in session
  async updateUser(user: User): Promise<void> {
    if (this.session) {
      this.session.user = user;
      this.storeSession(this.session);
    }
  }

  // Refresh session with new token
  async refreshSession(newToken: string, newRefreshToken?: string): Promise<void> {
    if (this.session) {
      this.session.token = newToken;
      if (newRefreshToken) {
        this.session.refreshToken = newRefreshToken;
      }
      this.session.expiresAt = new Date(Date.now() + TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY);
      this.storeSession(this.session);
    }
  }

  // Private methods
  private getStoredSession(): Session | null {
    try {
      const sessionData = storage.get(STORAGE_KEYS.USER);
      const token = storage.get(STORAGE_KEYS.ACCESS_TOKEN);
      const refreshToken = storage.get(STORAGE_KEYS.REFRESH_TOKEN);

      if (!sessionData || !token) {
        return null;
      }

      const user = JSON.parse(sessionData);
      const expiresAt = new Date(Date.now() + TOKEN_CONFIG.ACCESS_TOKEN_EXPIRY);

      return {
        user,
        token,
        refreshToken: refreshToken || undefined,
        expiresAt,
      };
    } catch {
      return null;
    }
  }

  private storeSession(session: Session): void {
    try {
      storage.set(STORAGE_KEYS.USER, JSON.stringify(session.user));
      storage.set(STORAGE_KEYS.ACCESS_TOKEN, session.token);
      
      if (session.refreshToken) {
        storage.set(STORAGE_KEYS.REFRESH_TOKEN, session.refreshToken);
      }

      // Also set cookies for SSR
      if (typeof document !== 'undefined') {
        this.setCookie(COOKIE_NAMES.ACCESS_TOKEN, session.token);
        if (session.refreshToken) {
          this.setCookie(COOKIE_NAMES.REFRESH_TOKEN, session.refreshToken);
        }
      }
    } catch (error) {
      console.error('Failed to store session:', error);
    }
  }

  private removeStoredSession(): void {
    try {
      storage.remove(STORAGE_KEYS.USER);
      storage.remove(STORAGE_KEYS.ACCESS_TOKEN);
      storage.remove(STORAGE_KEYS.REFRESH_TOKEN);

      // Also remove cookies
      if (typeof document !== 'undefined') {
        this.removeCookie(COOKIE_NAMES.ACCESS_TOKEN);
        this.removeCookie(COOKIE_NAMES.REFRESH_TOKEN);
        this.removeCookie(COOKIE_NAMES.SESSION_ID);
      }
    } catch (error) {
      console.error('Failed to remove session:', error);
    }
  }

  private isSessionExpired(session: Session): boolean {
    if (!session.token) return true;
    
    // Check token expiration
    if (isTokenExpired(session.token)) {
      return true;
    }

    // Check session expiration
    return new Date() > session.expiresAt;
  }

  private setCookie(name: string, value: string, days: number = 7): void {
    const expires = new Date();
    expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000);
    document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/;secure;samesite=strict`;
  }

  private removeCookie(name: string): void {
    document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/;`;
  }

  // Get session from cookies (for SSR)
  getSessionFromCookies(cookieString: string): Session | null {
    try {
      const cookies = this.parseCookies(cookieString);
      const token = cookies[COOKIE_NAMES.ACCESS_TOKEN];
      const refreshToken = cookies[COOKIE_NAMES.REFRESH_TOKEN];

      if (!token) return null;

      // Note: In a real implementation, you'd validate the token
      // and extract user data from it or fetch from database
      return null; // Placeholder - implement based on your token strategy
    } catch {
      return null;
    }
  }

  private parseCookies(cookieString: string): Record<string, string> {
    const cookies: Record<string, string> = {};
    
    cookieString.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });

    return cookies;
  }
}

export const sessionService = new SessionService();
