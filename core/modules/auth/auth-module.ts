// Auth module definition using better-auth

import { defineModule } from '@core/system/factories';
import { z } from 'zod';
import type { AuthConfig } from './types';

// Configuration schema
const authConfigSchema = z.object({
  providers: z.array(z.object({
    id: z.string(),
    name: z.string(),
    type: z.enum(['oauth', 'credentials']),
    config: z.record(z.any()).optional(),
  })),
  sessionDuration: z.number().positive(),
  cookieName: z.string(),
  redirects: z.object({
    login: z.string(),
    logout: z.string(),
    afterLogin: z.string(),
    afterLogout: z.string(),
  }),
});

export const authModule = defineModule({
  name: 'auth',
  version: '1.0.0',
  dependencies: ['database'], // Requires database module

  async setup(context) {
    const { config, registerService, getService, events, logger } = context;
    
    // Validate configuration
    const authConfig = authConfigSchema.parse(config) as AuthConfig;
    logger.info('Auth module configuration validated');

    // Get database service
    const db = getService('database');
    if (!db) {
      throw new Error('Database service not found. Auth module requires database module.');
    }

    // Initialize better-auth
    const { betterAuth } = await import('better-auth');
    const { drizzleAdapter } = await import('better-auth/adapters/drizzle');

    const auth = betterAuth({
      database: drizzleAdapter(db, {
        provider: 'pg', // or your database provider
      }),
      emailAndPassword: {
        enabled: authConfig.providers.some(p => p.type === 'credentials'),
        requireEmailVerification: false, // Configure as needed
      },
      socialProviders: {
        google: authConfig.providers.find(p => p.id === 'google')?.config || undefined,
        github: authConfig.providers.find(p => p.id === 'github')?.config || undefined,
      },
      session: {
        expiresIn: authConfig.sessionDuration,
        updateAge: authConfig.sessionDuration / 4, // Refresh session when 1/4 time remaining
        cookieName: authConfig.cookieName,
      },
      advanced: {
        generateId: () => crypto.randomUUID(), // Use Web API
      },
    });

    // Create auth service
    const authService = {
      // Core auth instance
      auth,

      // Sign up with email and password
      async signUp(email: string, password: string, name: string) {
        try {
          const result = await auth.api.signUpEmail({
            body: { email, password, name },
          });
          
          if (result.user && result.session) {
            events.emit('auth:signup', { user: result.user, session: result.session });
            logger.info(`User signed up: ${result.user.email}`);
          }
          
          return result;
        } catch (error) {
          logger.error('Sign up failed:', error);
          throw error;
        }
      },

      // Sign in with email and password
      async signIn(email: string, password: string, rememberMe = false) {
        try {
          const result = await auth.api.signInEmail({
            body: { email, password, rememberMe },
          });
          
          if (result.user && result.session) {
            events.emit('auth:login', { user: result.user, session: result.session });
            logger.info(`User signed in: ${result.user.email}`);
          }
          
          return result;
        } catch (error) {
          logger.error('Sign in failed:', error);
          throw error;
        }
      },

      // Sign out
      async signOut() {
        try {
          const session = await this.getSession();
          const result = await auth.api.signOut();
          
          if (session?.user) {
            events.emit('auth:logout', { userId: session.user.id });
            logger.info(`User signed out: ${session.user.email}`);
          }
          
          return result;
        } catch (error) {
          logger.error('Sign out failed:', error);
          throw error;
        }
      },

      // Get current session
      async getSession() {
        try {
          return await auth.api.getSession();
        } catch (error) {
          logger.error('Get session failed:', error);
          return null;
        }
      },

      // Get current user
      async getUser() {
        const session = await this.getSession();
        return session?.user || null;
      },

      // Verify email
      async verifyEmail(token: string) {
        try {
          const result = await auth.api.verifyEmail({
            body: { token },
          });
          
          if (result.user) {
            events.emit('auth:email-verified', { userId: result.user.id });
            logger.info(`Email verified for user: ${result.user.email}`);
          }
          
          return result;
        } catch (error) {
          logger.error('Email verification failed:', error);
          throw error;
        }
      },

      // Reset password
      async resetPassword(email: string) {
        try {
          const result = await auth.api.forgetPassword({
            body: { email },
          });
          
          events.emit('auth:password-reset', { email });
          logger.info(`Password reset requested for: ${email}`);
          
          return result;
        } catch (error) {
          logger.error('Password reset failed:', error);
          throw error;
        }
      },

      // Update password
      async updatePassword(currentPassword: string, newPassword: string) {
        try {
          return await auth.api.changePassword({
            body: { currentPassword, newPassword },
          });
        } catch (error) {
          logger.error('Password update failed:', error);
          throw error;
        }
      },

      // Middleware for Next.js API routes
      middleware: auth.handler,

      // Client-side auth methods
      client: {
        signUp: auth.signUp,
        signIn: auth.signIn,
        signOut: auth.signOut,
        useSession: auth.useSession,
      },
    };

    // Register the auth service
    registerService('auth', authService);
    
    // Set up session monitoring
    events.on('auth:login', ({ session }) => {
      // Set up session expiration monitoring
      const timeUntilExpiry = session.expiresAt.getTime() - Date.now();
      if (timeUntilExpiry > 0) {
        setTimeout(() => {
          events.emit('auth:session-expired', { userId: session.userId });
        }, timeUntilExpiry);
      }
    });

    logger.info('Auth module initialized successfully');
  },

  async cleanup() {
    // Cleanup logic if needed
  },
});
