# Auth Module

## Overview

The Auth module provides comprehensive authentication and session management functionality for the Next.js application. It handles user login, signup, session management, and token validation with support for both client-side and server-side operations.

## Features

- User authentication (login/signup)
- Session management with automatic token refresh
- Password validation and security
- JWT token handling
- Local storage and cookie management
- Server-side session validation
- Type-safe error handling
- OpenNext/Cloudflare Workers compatibility

## Structure

```
core/modules/auth/
├── types/              # TypeScript type definitions
├── constants/          # Auth-related constants and configuration
├── utils/              # Utility functions for validation and token handling
├── services/           # Business logic services
├── components/         # React components (to be implemented)
├── hooks/              # React hooks (to be implemented)
├── stores/             # State management (to be implemented)
├── __tests__/          # Unit and integration tests (to be implemented)
├── index.ts            # Public API exports
└── README.md           # This file
```

## Types

### Core Types
- `User` - User entity with profile information
- `AuthState` - Authentication state for UI components
- `LoginCredentials` - Login form data
- `SignupData` - Signup form data
- `Session` - Session data with tokens and expiration
- `TokenPayload` - JWT token payload structure

### Error Types
- `AuthError` - Authentication-specific errors
- `ValidationError` - Form validation errors

## Services

### SessionService
Manages user sessions, tokens, and storage.

```typescript
import { sessionService } from '@/auth';

// Get current session
const session = await sessionService.getSession();

// Set new session
await sessionService.setSession({ user, token, refreshToken });

// Clear session
await sessionService.clearSession();

// Check if session is valid
const isValid = await sessionService.isValidSession();
```

## Utils

### Validation Functions
```typescript
import { validateEmail, validatePassword, validateCredentials } from '@/auth';

// Validate email format
const isValidEmail = validateEmail('<EMAIL>');

// Validate password strength
const isValidPassword = validatePassword('mypassword123');

// Validate login credentials
try {
  validateCredentials(email, password);
} catch (error) {
  console.error('Validation failed:', error.message);
}
```

### Token Utilities
```typescript
import { parseJwtPayload, isTokenExpired } from '@/auth';

// Parse JWT payload
const payload = parseJwtPayload(token);

// Check if token is expired
const expired = isTokenExpired(token);
```

### Storage Utilities
```typescript
import { storage } from '@/auth';

// Safe localStorage operations
storage.set('key', 'value');
const value = storage.get('key');
storage.remove('key');
```

## Constants

### API Endpoints
```typescript
import { AUTH_ENDPOINTS } from '@/auth';

// Use predefined endpoints
fetch(AUTH_ENDPOINTS.LOGIN, { ... });
```

### Error Messages
```typescript
import { ERROR_MESSAGES } from '@/auth';

// Use consistent error messages
throw new Error(ERROR_MESSAGES.INVALID_CREDENTIALS);
```

### Storage Keys
```typescript
import { STORAGE_KEYS, COOKIE_NAMES } from '@/auth';

// Use consistent storage keys
localStorage.setItem(STORAGE_KEYS.ACCESS_TOKEN, token);
```

## Usage Examples

### Basic Authentication Flow
```typescript
import { sessionService, validateCredentials, AuthError } from '@/auth';

// Login function
async function login(email: string, password: string) {
  try {
    // Validate credentials
    validateCredentials(email, password);
    
    // Make API call (implement authService)
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });
    
    if (!response.ok) {
      throw new AuthError('Login failed', 'LOGIN_FAILED');
    }
    
    const { user, token, refreshToken } = await response.json();
    
    // Store session
    await sessionService.setSession({ user, token, refreshToken });
    
    return user;
  } catch (error) {
    console.error('Login error:', error);
    throw error;
  }
}
```

### Session Validation in API Routes
```typescript
import { sessionService } from '@/auth';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Extract token from request
    const token = request.headers.get('authorization')?.replace('Bearer ', '');
    
    if (!token) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Validate token (implement in authService)
    const user = await validateToken(token);
    
    if (!user) {
      return NextResponse.json({ error: 'Invalid token' }, { status: 401 });
    }
    
    // Continue with authenticated request
    return NextResponse.json({ user });
  } catch (error) {
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
```

## Integration with Next.js

### Middleware Integration
```typescript
// middleware.ts
import { sessionService } from '@/auth';

export async function middleware(request: NextRequest) {
  const session = await sessionService.getSession();
  
  if (!session && request.nextUrl.pathname.startsWith('/dashboard')) {
    return NextResponse.redirect(new URL('/login', request.url));
  }
  
  return NextResponse.next();
}
```

### Page Integration
```typescript
// app/dashboard/page.tsx
import { sessionService } from '@/auth';

export default async function DashboardPage() {
  const session = await sessionService.getSession();
  
  if (!session) {
    redirect('/login');
  }
  
  return <div>Welcome, {session.user.name}!</div>;
}
```

## OpenNext Compatibility

This module is designed to work with OpenNext and Cloudflare Workers:

- Uses Web APIs instead of Node.js specific APIs
- Handles both client-side and server-side operations
- Supports Edge Runtime for API routes
- Optimized for serverless environments

## Dependencies

### Internal Dependencies
- None (this is a core module)

### External Dependencies
- Next.js (built-in APIs)
- TypeScript (for type safety)

## Testing

Tests will be implemented in the `__tests__/` directory covering:
- Service functionality
- Utility functions
- Error handling
- Integration scenarios

## Future Enhancements

- React components (LoginForm, SignupForm, AuthGuard)
- React hooks (useAuth, useLogin, useSignup)
- State management with Zustand
- OAuth integration
- Multi-factor authentication
- Password reset functionality
- Email verification

## Contributing

When adding new functionality to this module:

1. Add types to `types/index.ts`
2. Add constants to `constants/index.ts`
3. Implement services in `services/`
4. Add utilities to `utils/index.ts`
5. Export public API in `index.ts`
6. Update this README
7. Add comprehensive tests
