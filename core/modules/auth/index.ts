// Types
export type {
  User,
  AuthState,
  LoginCredentials,
  SignupData,
  Session,
  TokenPayload,
  PasswordResetRequest,
  PasswordReset,
  AuthEvent,
} from './types';

// Errors
export { AuthError, ValidationError } from './types';

// Services
export { sessionService } from './services/sessionService';

// Utils
export {
  validateEmail,
  validatePassword,
  validateName,
  validateCredentials,
  validateSignupData,
  parseJwtPayload,
  isTokenExpired,
  generateSecureId,
  formatUserDisplayName,
  getUserInitials,
  sanitizeUser,
  isBrowser,
  storage,
  debounce,
} from './utils';

// Constants
export {
  AUTH_ENDPOINTS,
  ERROR_MESSAGES,
  STORAGE_KEYS,
  COOKIE_NAMES,
  TOKEN_CONFIG,
  VAL<PERSON>ATION_RULES,
  AUTH_ROUTES,
  USER_ROLES,
  AUTH_EVENTS,
} from './constants';

// Note: Components, hooks, and other services will be added as they are implemented
// This follows the modular architecture pattern where each module exports its public API
