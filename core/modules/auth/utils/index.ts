import { VAL<PERSON><PERSON>ION_RULES, ERROR_MESSAGES } from '../constants';
import { ValidationError } from '../types';

// Email validation
export function validateEmail(email: string): boolean {
  return VALIDATION_RULES.EMAIL_REGEX.test(email);
}

// Password validation
export function validatePassword(password: string): boolean {
  return password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH;
}

// Name validation
export function validateName(name: string): boolean {
  return name.length >= VALIDATION_RULES.NAME_MIN_LENGTH && 
         name.length <= VALIDATION_RULES.NAME_MAX_LENGTH;
}

// Comprehensive credential validation
export function validateCredentials(email: string, password: string): void {
  if (!email) {
    throw new ValidationError(ERROR_MESSAGES.REQUIRED_FIELD, 'email');
  }
  
  if (!validateEmail(email)) {
    throw new ValidationError(ERROR_MESSAGES.INVALID_EMAIL, 'email');
  }
  
  if (!password) {
    throw new ValidationError(ERROR_MESSAGES.REQUIRED_FIELD, 'password');
  }
  
  if (!validatePassword(password)) {
    throw new ValidationError(ERROR_MESSAGES.WEAK_PASSWORD, 'password');
  }
}

// Signup data validation
export function validateSignupData(data: {
  email: string;
  password: string;
  confirmPassword: string;
  name: string;
}): void {
  const { email, password, confirmPassword, name } = data;
  
  if (!name) {
    throw new ValidationError(ERROR_MESSAGES.REQUIRED_FIELD, 'name');
  }
  
  if (!validateName(name)) {
    throw new ValidationError('Name must be between 2 and 50 characters', 'name');
  }
  
  validateCredentials(email, password);
  
  if (password !== confirmPassword) {
    throw new ValidationError(ERROR_MESSAGES.PASSWORDS_DONT_MATCH, 'confirmPassword');
  }
}

// JWT token parsing (client-safe)
export function parseJwtPayload(token: string): any {
  try {
    const base64Url = token.split('.')[1];
    const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
    const jsonPayload = decodeURIComponent(
      atob(base64)
        .split('')
        .map(c => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
        .join('')
    );
    return JSON.parse(jsonPayload);
  } catch {
    return null;
  }
}

// Check if token is expired
export function isTokenExpired(token: string): boolean {
  const payload = parseJwtPayload(token);
  if (!payload || !payload.exp) return true;
  
  const currentTime = Math.floor(Date.now() / 1000);
  return payload.exp < currentTime;
}

// Generate secure random string
export function generateSecureId(length: number = 32): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const array = new Uint8Array(length);
    crypto.getRandomValues(array);
    for (let i = 0; i < length; i++) {
      result += chars[array[i] % chars.length];
    }
  } else {
    // Fallback for environments without crypto
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

// Format user display name
export function formatUserDisplayName(user: { name: string; email: string }): string {
  return user.name || user.email.split('@')[0];
}

// Get user initials for avatar
export function getUserInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part.charAt(0).toUpperCase())
    .slice(0, 2)
    .join('');
}

// Sanitize user data for client
export function sanitizeUser(user: any): any {
  const { password, ...sanitized } = user;
  return sanitized;
}

// Check if running in browser
export function isBrowser(): boolean {
  return typeof window !== 'undefined';
}

// Safe localStorage operations
export const storage = {
  get: (key: string): string | null => {
    if (!isBrowser()) return null;
    try {
      return localStorage.getItem(key);
    } catch {
      return null;
    }
  },
  
  set: (key: string, value: string): void => {
    if (!isBrowser()) return;
    try {
      localStorage.setItem(key, value);
    } catch {
      // Handle storage quota exceeded or other errors
    }
  },
  
  remove: (key: string): void => {
    if (!isBrowser()) return;
    try {
      localStorage.removeItem(key);
    } catch {
      // Handle errors silently
    }
  },
  
  clear: (): void => {
    if (!isBrowser()) return;
    try {
      localStorage.clear();
    } catch {
      // Handle errors silently
    }
  }
};

// Debounce function for form validation
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}
