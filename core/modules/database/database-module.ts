// Database module using Drizzle ORM

import { defineModule } from '@core/system/factories';
import { z } from 'zod';

// Configuration schema
const databaseConfigSchema = z.object({
  provider: z.enum(['postgresql', 'mysql', 'sqlite']),
  url: z.string().url(),
  migrations: z.object({
    enabled: z.boolean(),
    directory: z.string(),
  }).optional(),
  logging: z.boolean().optional(),
});

export const databaseModule = defineModule({
  name: 'database',
  version: '1.0.0',
  dependencies: [], // No dependencies

  async setup(context) {
    const { config, registerService, events, logger } = context;
    
    // Validate configuration
    const dbConfig = databaseConfigSchema.parse(config);
    logger.info('Database module configuration validated');

    try {
      let db;
      
      // Initialize based on provider
      switch (dbConfig.provider) {
        case 'postgresql': {
          const { drizzle } = await import('drizzle-orm/postgres-js');
          const postgres = await import('postgres');
          
          const client = postgres(dbConfig.url, {
            max: 10,
            idle_timeout: 20,
            connect_timeout: 10,
          });
          
          db = drizzle(client, {
            logger: dbConfig.logging,
          });
          
          // Test connection
          await client`SELECT 1`;
          break;
        }
        
        case 'mysql': {
          const { drizzle } = await import('drizzle-orm/mysql2');
          const mysql = await import('mysql2/promise');
          
          const connection = await mysql.createConnection(dbConfig.url);
          db = drizzle(connection, {
            logger: dbConfig.logging,
          });
          
          // Test connection
          await connection.execute('SELECT 1');
          break;
        }
        
        case 'sqlite': {
          const { drizzle } = await import('drizzle-orm/better-sqlite3');
          const Database = await import('better-sqlite3');
          
          const sqlite = new Database.default(dbConfig.url);
          db = drizzle(sqlite, {
            logger: dbConfig.logging,
          });
          break;
        }
        
        default:
          throw new Error(`Unsupported database provider: ${dbConfig.provider}`);
      }

      // Run migrations if enabled
      if (dbConfig.migrations?.enabled) {
        const { migrate } = await import('drizzle-orm/postgres-js/migrator');
        await migrate(db, { migrationsFolder: dbConfig.migrations.directory });
        logger.info('Database migrations completed');
      }

      // Create database service
      const databaseService = {
        // Core database instance
        db,
        
        // Query helpers
        async query(sql: string, params?: any[]) {
          try {
            return await db.execute(sql, params);
          } catch (error) {
            logger.error('Database query failed:', error);
            events.emit('db:error', { error });
            throw error;
          }
        },

        // Transaction helper
        async transaction<T>(callback: (tx: any) => Promise<T>): Promise<T> {
          return await db.transaction(callback);
        },

        // Health check
        async healthCheck() {
          try {
            switch (dbConfig.provider) {
              case 'postgresql':
                await db.execute('SELECT 1');
                break;
              case 'mysql':
                await db.execute('SELECT 1');
                break;
              case 'sqlite':
                await db.run('SELECT 1');
                break;
            }
            return { status: 'healthy', provider: dbConfig.provider };
          } catch (error) {
            logger.error('Database health check failed:', error);
            return { status: 'unhealthy', error: error.message };
          }
        },

        // Get connection info
        getConnectionInfo() {
          return {
            provider: dbConfig.provider,
            // Don't expose sensitive connection details
            connected: true,
          };
        },
      };

      // Register the database service
      registerService('database', databaseService);
      
      // Emit connection event
      events.emit('db:connected', { provider: dbConfig.provider });
      
      logger.info(`Database module initialized with ${dbConfig.provider}`);
    } catch (error) {
      logger.error('Database initialization failed:', error);
      events.emit('db:error', { error });
      throw error;
    }
  },

  async cleanup() {
    // Cleanup database connections if needed
    // This would depend on the specific database client
  },
});
