// Application initialization script

import { app } from '@system';
import config from '../config';

// Import modules
import { authModule } from '@modules/auth/auth-module';
import { databaseModule } from '@modules/database/database-module';

// Import plugins
import { analyticsPlugin } from '@plugins/analytics/analytics-plugin';

// Initialize application
export async function initializeApp() {
  try {
    // Set configuration
    app.setConfig(config);

    // Register modules (order matters for dependencies)
    app.registerModule(databaseModule);
    app.registerModule(authModule);

    // Register plugins
    app.registerPlugin(analyticsPlugin);

    // Initialize everything
    await app.initialize();

    return app;
  } catch (error) {
    console.error('Failed to initialize application:', error);
    throw error;
  }
}

// Cleanup function
export async function cleanupApp() {
  try {
    await app.cleanup();
  } catch (error) {
    console.error('Failed to cleanup application:', error);
  }
}

// Get app status
export function getAppStatus() {
  return app.getStatus();
}

// Re-export for convenience
export { app, config };
