# Next.js + OpenNext Modular Architecture Implementation Guide

## Table of Contents
1. [Overview](#overview)
2. [Architecture Principles](#architecture-principles)
3. [Module Structure Standards](#module-structure-standards)
4. [File Naming Conventions](#file-naming-conventions)
5. [TypeScript Configuration](#typescript-configuration)
6. [Module Creation Workflow](#module-creation-workflow)
7. [Inter-Module Communication](#inter-module-communication)
8. [Next.js App Router Integration](#nextjs-app-router-integration)
9. [OpenNext Compatibility](#opennext-compatibility)
10. [Testing Strategy](#testing-strategy)
11. [Example Module Implementation](#example-module-implementation)
12. [Best Practices](#best-practices)

## Overview

This guide establishes a comprehensive modular architecture for Next.js applications deployed with OpenNext. The architecture promotes code organization, maintainability, and scalability through feature-based modules under the `core/modules/` directory structure.

### Key Benefits
- **Isolation**: Each feature is completely self-contained
- **Reusability**: Modules can be easily shared across projects
- **Maintainability**: Clear boundaries and responsibilities
- **Scalability**: Easy to add new features without affecting existing code
- **Testing**: Isolated testing of individual modules
- **Team Collaboration**: Multiple developers can work on different modules

## Architecture Principles

### 1. Feature-Based Organization
Each module represents a complete feature or domain area (auth, rbac, database, etc.)

### 2. Dependency Direction
- Modules can depend on shared utilities
- Higher-level modules can depend on lower-level modules
- Avoid circular dependencies
- Use dependency injection for loose coupling

### 3. Public API Design
Each module exposes a clean, well-defined public API through its `index.ts` file

### 4. Single Responsibility
Each module has a single, well-defined responsibility

## Module Structure Standards

### Standard Directory Structure
```
core/modules/{module-name}/
├── components/          # React components specific to this module
│   ├── ui/             # Pure UI components
│   ├── forms/          # Form components
│   └── guards/         # Guard/wrapper components
├── hooks/              # Custom React hooks
├── services/           # Business logic and API calls
├── utils/              # Pure utility functions
├── types/              # TypeScript type definitions
├── constants/          # Module constants and enums
├── stores/             # State management (Zustand/Redux)
├── schemas/            # Validation schemas (Zod, Yup, etc.)
├── __tests__/          # Unit and integration tests
│   ├── components/     # Component tests
│   ├── hooks/          # Hook tests
│   ├── services/       # Service tests
│   └── utils/          # Utility tests
├── index.ts            # Main module export (public API)
└── README.md           # Module documentation
```

### Core Modules to Implement

#### 1. `core/modules/auth/`
Authentication and session management
- Components: LoginForm, SignupForm, AuthGuard, LogoutButton
- Hooks: useAuth, useSession, useLogin, useSignup
- Services: authService, sessionService, tokenService
- Types: User, AuthState, LoginCredentials, SignupData

#### 2. `core/modules/rbac/`
Role-based access control
- Components: PermissionGuard, RoleSelector, AccessControl
- Hooks: usePermissions, useRoles, useAccessControl
- Services: rbacService, permissionService, roleService
- Types: Role, Permission, AccessControl, PolicyRule

#### 3. `core/modules/database/`
Database integration and queries
- Services: dbService, queryBuilder, migrationService
- Utils: connectionManager, queryHelpers, schemaValidator
- Types: DatabaseConfig, QueryResult, Migration, Schema

#### 4. `core/modules/firebase/`
Firebase integration
- Services: firebaseService, firestoreService, storageService
- Utils: firebaseConfig, firebaseAuth, firebaseAnalytics
- Types: FirebaseConfig, FirebaseUser, FirestoreDocument

#### 5. `core/modules/ui/`
Shared UI components and design system
- Components: Button, Input, Modal, Card, Layout
- Hooks: useTheme, useBreakpoint, useModal, useToast
- Types: ThemeConfig, ComponentProps, BreakpointConfig

#### 6. `core/modules/api/`
API utilities and HTTP client
- Services: apiClient, requestHandler, responseHandler
- Utils: errorHandler, requestInterceptor, responseParser
- Types: ApiResponse, RequestConfig, ErrorResponse

## File Naming Conventions

### Components
- **Format**: PascalCase with descriptive names
- **Examples**: `LoginForm.tsx`, `UserProfile.tsx`, `PermissionGuard.tsx`
- **Pattern**: `{Feature}{ComponentType}.tsx`

### Hooks
- **Format**: camelCase starting with 'use'
- **Examples**: `useAuth.ts`, `usePermissions.ts`, `useApiClient.ts`
- **Pattern**: `use{Feature}.ts`

### Services
- **Format**: camelCase ending with 'Service'
- **Examples**: `authService.ts`, `apiService.ts`, `dbService.ts`
- **Pattern**: `{feature}Service.ts`

### Utils
- **Format**: camelCase with descriptive names
- **Examples**: `formatDate.ts`, `validateEmail.ts`, `parseJwt.ts`
- **Pattern**: `{action}{Subject}.ts`

### Types
- **Format**: PascalCase with descriptive names
- **Examples**: `User.ts`, `ApiResponse.ts`, `DatabaseConfig.ts`
- **Pattern**: `{Entity}.ts` or `{Feature}Types.ts`

### Constants
- **Format**: SCREAMING_SNAKE_CASE
- **Examples**: `API_ENDPOINTS.ts`, `ERROR_MESSAGES.ts`, `DEFAULT_CONFIG.ts`
- **Pattern**: `{CATEGORY}_{TYPE}.ts`

## TypeScript Configuration

### Update tsconfig.json
```json
{
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@/*": ["./*"],
      "@core/*": ["./core/*"],
      "@modules/*": ["./core/modules/*"],
      "@auth": ["./core/modules/auth"],
      "@rbac": ["./core/modules/rbac"],
      "@database": ["./core/modules/database"],
      "@firebase": ["./core/modules/firebase"],
      "@ui": ["./core/modules/ui"],
      "@api": ["./core/modules/api"]
    }
  }
}
```

### Module Export Pattern (index.ts)
```typescript
// core/modules/{module-name}/index.ts

// Export all public components
export * from './components';

// Export all public hooks
export * from './hooks';

// Export all public services
export * from './services';

// Export all public types
export * from './types';

// Export all public utils
export * from './utils';

// Export all public constants
export * from './constants';

// Export default configuration if applicable
export { default as config } from './config';
```

### Type-Only Exports
```typescript
// For type-only exports to avoid runtime imports
export type { User, AuthState } from './types';
export type { LoginCredentials, SignupData } from './types';
```

## Module Creation Workflow

### Step 1: Create Module Directory Structure
```bash
mkdir -p core/modules/{module-name}/{components,hooks,services,utils,types,constants,stores,schemas,__tests__}
```

### Step 2: Create index.ts
```typescript
// core/modules/{module-name}/index.ts
export * from './components';
export * from './hooks';
export * from './services';
export * from './types';
export * from './utils';
export * from './constants';
```

### Step 3: Create README.md
```markdown
# {Module Name} Module

## Overview
Brief description of the module's purpose and functionality.

## Components
- List of exported components

## Hooks
- List of exported hooks

## Services
- List of exported services

## Types
- List of exported types

## Usage Examples
```typescript
import { ComponentName, useHookName } from '@modules/{module-name}';
```

## Dependencies
- List of internal and external dependencies
```

### Step 4: Implement Core Functionality
Start with types, then services, then components and hooks.

### Step 5: Add Tests
Create comprehensive tests for all public APIs.

### Step 6: Update Documentation
Keep README.md and inline documentation up to date.

## Inter-Module Communication

### 1. Direct Imports (Preferred)
```typescript
// In a component
import { authService } from '@/auth';
import { rbacService } from '@/rbac';

const MyComponent = () => {
  const user = authService.getCurrentUser();
  const permissions = rbacService.getUserPermissions(user.id);
  // ...
};
```

### 2. Dependency Injection
```typescript
// Service with dependencies
export class UserService {
  constructor(
    private authService: AuthService,
    private rbacService: RbacService
  ) {}

  async getUserWithPermissions(userId: string) {
    const user = await this.authService.getUser(userId);
    const permissions = await this.rbacService.getUserPermissions(userId);
    return { ...user, permissions };
  }
}
```

### 3. Event-Driven Communication
```typescript
// Using a simple event emitter for loose coupling
import { EventEmitter } from '@/core/utils/eventEmitter';

// In auth module
authService.on('user:login', (user) => {
  EventEmitter.emit('auth:userLoggedIn', user);
});

// In analytics module
EventEmitter.on('auth:userLoggedIn', (user) => {
  analyticsService.track('user_login', { userId: user.id });
});
```

### 4. Shared State Management
```typescript
// Using Zustand for shared state
import { create } from 'zustand';

interface AppState {
  user: User | null;
  permissions: Permission[];
  setUser: (user: User | null) => void;
  setPermissions: (permissions: Permission[]) => void;
}

export const useAppStore = create<AppState>((set) => ({
  user: null,
  permissions: [],
  setUser: (user) => set({ user }),
  setPermissions: (permissions) => set({ permissions }),
}));
```

## Next.js App Router Integration

### 1. Page Integration
```typescript
// app/dashboard/page.tsx
import { AuthGuard } from '@/auth';
import { PermissionGuard } from '@/rbac';
import { DashboardLayout } from '@/ui';

export default function DashboardPage() {
  return (
    <AuthGuard>
      <PermissionGuard permission="dashboard:read">
        <DashboardLayout>
          {/* Page content */}
        </DashboardLayout>
      </PermissionGuard>
    </AuthGuard>
  );
}
```

### 2. API Route Integration
```typescript
// app/api/users/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/auth';
import { dbService } from '@/database';

export async function GET(request: NextRequest) {
  try {
    const user = await authService.validateRequest(request);
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const users = await dbService.query('SELECT * FROM users');
    return NextResponse.json(users);
  } catch (error) {
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
```

### 3. Middleware Integration
```typescript
// middleware.ts
import { NextRequest, NextResponse } from 'next/server';
import { authService } from '@/auth';
import { rbacService } from '@/rbac';

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Skip auth for public routes
  if (pathname.startsWith('/api/public') || pathname === '/login') {
    return NextResponse.next();
  }

  // Validate authentication
  const user = await authService.validateRequest(request);
  if (!user) {
    return NextResponse.redirect(new URL('/login', request.url));
  }

  // Check permissions for protected routes
  if (pathname.startsWith('/admin')) {
    const hasPermission = await rbacService.checkPermission(user.id, 'admin:access');
    if (!hasPermission) {
      return NextResponse.redirect(new URL('/unauthorized', request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: ['/((?!_next/static|_next/image|favicon.ico).*)'],
};
```

### 4. Layout Integration
```typescript
// app/layout.tsx
import { AuthProvider } from '@/auth';
import { ThemeProvider } from '@/ui';
import { Toaster } from '@/ui';

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <ThemeProvider>
          <AuthProvider>
            {children}
            <Toaster />
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
```

## OpenNext Compatibility

### 1. Runtime Compatibility
- **Edge Runtime**: Ensure all API routes work with Edge Runtime
- **Cloudflare Workers**: No Node.js specific APIs in client code
- **Web APIs**: Use Web APIs instead of Node.js APIs where possible

```typescript
// ❌ Avoid Node.js APIs in client code
import fs from 'fs';
import path from 'path';

// ✅ Use Web APIs or server-only code
export const runtime = 'edge'; // For API routes

// ✅ Use dynamic imports for server-only code
const getServerConfig = async () => {
  if (typeof window === 'undefined') {
    const { readFileSync } = await import('fs');
    return readFileSync('./config.json', 'utf-8');
  }
  return null;
};
```

### 2. Bundle Optimization
```typescript
// Use dynamic imports for code splitting
const LazyComponent = dynamic(() => import('@/ui/components/HeavyComponent'), {
  loading: () => <div>Loading...</div>,
});

// Tree-shaking friendly exports
export { Button } from './Button';
export { Input } from './Input';
// Instead of: export * from './components';
```

### 3. Environment Variables
```typescript
// core/modules/config/environment.ts
export const env = {
  // Client-side variables (prefixed with NEXT_PUBLIC_)
  apiUrl: process.env.NEXT_PUBLIC_API_URL!,

  // Server-side variables
  databaseUrl: process.env.DATABASE_URL!,
  jwtSecret: process.env.JWT_SECRET!,
} as const;

// Validate environment variables at build time
const requiredEnvVars = ['NEXT_PUBLIC_API_URL', 'DATABASE_URL', 'JWT_SECRET'];
requiredEnvVars.forEach((envVar) => {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
});
```

## Testing Strategy

### 1. Unit Tests (Vitest)
```typescript
// core/modules/auth/__tests__/services/authService.test.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { authService } from '../services/authService';

describe('AuthService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should login user with valid credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'password' };
    const mockUser = { id: '1', email: '<EMAIL>' };

    vi.spyOn(authService, 'login').mockResolvedValue(mockUser);

    const result = await authService.login(credentials);
    expect(result).toEqual(mockUser);
  });

  it('should throw error with invalid credentials', async () => {
    const credentials = { email: '<EMAIL>', password: 'wrong' };

    vi.spyOn(authService, 'login').mockRejectedValue(new Error('Invalid credentials'));

    await expect(authService.login(credentials)).rejects.toThrow('Invalid credentials');
  });
});
```

### 2. Component Tests (React Testing Library)
```typescript
// core/modules/auth/__tests__/components/LoginForm.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { LoginForm } from '../components/LoginForm';

const mockLogin = vi.fn();
vi.mock('../hooks/useAuth', () => ({
  useAuth: () => ({
    login: mockLogin,
    isLoading: false,
    error: null,
  }),
}));

describe('LoginForm', () => {
  it('should render login form', () => {
    render(<LoginForm />);

    expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /login/i })).toBeInTheDocument();
  });

  it('should call login on form submission', async () => {
    render(<LoginForm />);

    fireEvent.change(screen.getByLabelText(/email/i), {
      target: { value: '<EMAIL>' },
    });
    fireEvent.change(screen.getByLabelText(/password/i), {
      target: { value: 'password' },
    });
    fireEvent.click(screen.getByRole('button', { name: /login/i }));

    await waitFor(() => {
      expect(mockLogin).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password',
      });
    });
  });
});
```

### 3. Hook Tests
```typescript
// core/modules/auth/__tests__/hooks/useAuth.test.ts
import { renderHook, act } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { useAuth } from '../hooks/useAuth';

describe('useAuth', () => {
  it('should initialize with null user', () => {
    const { result } = renderHook(() => useAuth());

    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });

  it('should update user state on login', async () => {
    const { result } = renderHook(() => useAuth());
    const mockUser = { id: '1', email: '<EMAIL>' };

    await act(async () => {
      await result.current.login({ email: '<EMAIL>', password: 'password' });
    });

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.isAuthenticated).toBe(true);
  });
});
```

### 4. Integration Tests
```typescript
// core/modules/auth/__tests__/integration/authFlow.test.ts
import { describe, it, expect } from 'vitest';
import { authService } from '../services/authService';
import { sessionService } from '../services/sessionService';

describe('Auth Integration', () => {
  it('should complete full authentication flow', async () => {
    // Login
    const user = await authService.login({
      email: '<EMAIL>',
      password: 'password',
    });

    // Verify session is created
    const session = await sessionService.getSession();
    expect(session.userId).toBe(user.id);

    // Logout
    await authService.logout();

    // Verify session is cleared
    const clearedSession = await sessionService.getSession();
    expect(clearedSession).toBeNull();
  });
});
```

### 5. Test Configuration
```typescript
// vitest.config.ts
import { defineConfig } from 'vitest/config';
import react from '@vitejs/plugin-react';
import path from 'path';

export default defineConfig({
  plugins: [react()],
  test: {
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    globals: true,
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './'),
      '@/core': path.resolve(__dirname, './core'),
      '@/modules': path.resolve(__dirname, './core/modules'),
      '@/auth': path.resolve(__dirname, './core/modules/auth'),
      '@/rbac': path.resolve(__dirname, './core/modules/rbac'),
      '@/database': path.resolve(__dirname, './core/modules/database'),
      '@/firebase': path.resolve(__dirname, './core/modules/firebase'),
      '@/ui': path.resolve(__dirname, './core/modules/ui'),
      '@/api': path.resolve(__dirname, './core/modules/api'),
    },
  },
});
```

## Example Module Implementation

### Auth Module Structure
```
core/modules/auth/
├── components/
│   ├── ui/
│   │   ├── LoginForm.tsx
│   │   ├── SignupForm.tsx
│   │   └── LogoutButton.tsx
│   ├── guards/
│   │   ├── AuthGuard.tsx
│   │   └── GuestGuard.tsx
│   └── index.ts
├── hooks/
│   ├── useAuth.ts
│   ├── useLogin.ts
│   ├── useSignup.ts
│   └── index.ts
├── services/
│   ├── authService.ts
│   ├── sessionService.ts
│   ├── tokenService.ts
│   └── index.ts
├── types/
│   ├── User.ts
│   ├── AuthState.ts
│   ├── Credentials.ts
│   └── index.ts
├── utils/
│   ├── validateCredentials.ts
│   ├── hashPassword.ts
│   └── index.ts
├── constants/
│   ├── AUTH_ENDPOINTS.ts
│   ├── ERROR_MESSAGES.ts
│   └── index.ts
├── stores/
│   ├── authStore.ts
│   └── index.ts
├── __tests__/
│   ├── components/
│   ├── hooks/
│   ├── services/
│   └── utils/
├── index.ts
└── README.md
```

### Example Implementation Files

#### 1. Auth Service
```typescript
// core/modules/auth/services/authService.ts
import { User, LoginCredentials, SignupData } from '../types';
import { apiClient } from '@/api';
import { sessionService } from './sessionService';

export class AuthService {
  async login(credentials: LoginCredentials): Promise<User> {
    const response = await apiClient.post('/auth/login', credentials);
    const { user, token } = response.data;

    await sessionService.setSession({ user, token });
    return user;
  }

  async signup(data: SignupData): Promise<User> {
    const response = await apiClient.post('/auth/signup', data);
    const { user, token } = response.data;

    await sessionService.setSession({ user, token });
    return user;
  }

  async logout(): Promise<void> {
    await apiClient.post('/auth/logout');
    await sessionService.clearSession();
  }

  async getCurrentUser(): Promise<User | null> {
    const session = await sessionService.getSession();
    return session?.user || null;
  }

  async validateRequest(request: Request): Promise<User | null> {
    const token = this.extractTokenFromRequest(request);
    if (!token) return null;

    try {
      const response = await apiClient.get('/auth/validate', {
        headers: { Authorization: `Bearer ${token}` },
      });
      return response.data.user;
    } catch {
      return null;
    }
  }

  private extractTokenFromRequest(request: Request): string | null {
    const authHeader = request.headers.get('authorization');
    return authHeader?.replace('Bearer ', '') || null;
  }
}

export const authService = new AuthService();
```

#### 2. Auth Hook
```typescript
// core/modules/auth/hooks/useAuth.ts
import { useState, useEffect, useCallback } from 'react';
import { User, LoginCredentials, SignupData } from '../types';
import { authService } from '../services';
import { useAuthStore } from '../stores/authStore';

export function useAuth() {
  const { user, setUser, clearUser } = useAuthStore();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const login = useCallback(async (credentials: LoginCredentials) => {
    setIsLoading(true);
    setError(null);

    try {
      const user = await authService.login(credentials);
      setUser(user);
      return user;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Login failed';
      setError(message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [setUser]);

  const signup = useCallback(async (data: SignupData) => {
    setIsLoading(true);
    setError(null);

    try {
      const user = await authService.signup(data);
      setUser(user);
      return user;
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Signup failed';
      setError(message);
      throw err;
    } finally {
      setIsLoading(false);
    }
  }, [setUser]);

  const logout = useCallback(async () => {
    setIsLoading(true);

    try {
      await authService.logout();
      clearUser();
    } catch (err) {
      console.error('Logout error:', err);
    } finally {
      setIsLoading(false);
    }
  }, [clearUser]);

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        const currentUser = await authService.getCurrentUser();
        if (currentUser) {
          setUser(currentUser);
        }
      } catch (err) {
        console.error('Auth initialization error:', err);
      }
    };

    initializeAuth();
  }, [setUser]);

  return {
    user,
    isAuthenticated: !!user,
    isLoading,
    error,
    login,
    signup,
    logout,
  };
}
```

#### 3. Auth Guard Component
```typescript
// core/modules/auth/components/guards/AuthGuard.tsx
import { ReactNode } from 'react';
import { redirect } from 'next/navigation';
import { useAuth } from '../../hooks/useAuth';
import { LoadingSpinner } from '@/ui';

interface AuthGuardProps {
  children: ReactNode;
  fallback?: ReactNode;
  redirectTo?: string;
}

export function AuthGuard({
  children,
  fallback,
  redirectTo = '/login'
}: AuthGuardProps) {
  const { isAuthenticated, isLoading } = useAuth();

  if (isLoading) {
    return fallback || <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    redirect(redirectTo);
  }

  return <>{children}</>;
}
```

#### 4. Module Index Export
```typescript
// core/modules/auth/index.ts

// Components
export { LoginForm } from './components/ui/LoginForm';
export { SignupForm } from './components/ui/SignupForm';
export { LogoutButton } from './components/ui/LogoutButton';
export { AuthGuard } from './components/guards/AuthGuard';
export { GuestGuard } from './components/guards/GuestGuard';

// Hooks
export { useAuth } from './hooks/useAuth';
export { useLogin } from './hooks/useLogin';
export { useSignup } from './hooks/useSignup';

// Services
export { authService } from './services/authService';
export { sessionService } from './services/sessionService';
export { tokenService } from './services/tokenService';

// Types
export type { User, AuthState, LoginCredentials, SignupData } from './types';

// Store
export { useAuthStore } from './stores/authStore';

// Constants
export { AUTH_ENDPOINTS, ERROR_MESSAGES } from './constants';
```

## Best Practices

### 1. Module Design Principles

#### Single Responsibility
Each module should have one clear purpose and responsibility.

```typescript
// ✅ Good: Focused on authentication
// core/modules/auth/

// ❌ Bad: Mixed responsibilities
// core/modules/auth-and-user-management/
```

#### Dependency Management
```typescript
// ✅ Good: Clear dependencies
import { apiClient } from '@/api';
import { User } from '@/auth/types';

// ❌ Bad: Circular dependencies
// auth module importing from rbac module that imports from auth
```

#### Public API Design
```typescript
// ✅ Good: Clean, focused API
export { useAuth, AuthGuard, authService };

// ❌ Bad: Exposing internal implementation
export { useAuth, AuthGuard, authService, InternalAuthHelper, _privateMethod };
```

### 2. Error Handling

#### Consistent Error Types
```typescript
// core/modules/shared/types/errors.ts
export class AuthError extends Error {
  constructor(message: string, public code: string) {
    super(message);
    this.name = 'AuthError';
  }
}

export class ValidationError extends Error {
  constructor(message: string, public field: string) {
    super(message);
    this.name = 'ValidationError';
  }
}
```

#### Error Boundaries
```typescript
// core/modules/ui/components/ErrorBoundary.tsx
import { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <div>Something went wrong.</div>;
    }

    return this.props.children;
  }
}
```

### 3. Performance Optimization

#### Lazy Loading
```typescript
// Lazy load heavy modules
const HeavyModule = lazy(() => import('@/heavy-module'));

// Code splitting at module level
const AdminPanel = lazy(() =>
  import('@/admin').then(module => ({ default: module.AdminPanel }))
);
```

#### Memoization
```typescript
// Memoize expensive computations
const memoizedPermissionCheck = useMemo(() =>
  rbacService.checkPermissions(user.id, requiredPermissions),
  [user.id, requiredPermissions]
);
```

### 4. Common Pitfalls to Avoid

#### 1. Circular Dependencies
```typescript
// ❌ Avoid
// auth/index.ts imports rbac
// rbac/index.ts imports auth

// ✅ Solution: Extract shared types to a common module
// core/modules/shared/types/
```

#### 2. Tight Coupling
```typescript
// ❌ Avoid direct database calls in components
const UserProfile = () => {
  const user = db.query('SELECT * FROM users WHERE id = ?', [userId]);
  // ...
};

// ✅ Use service layer
const UserProfile = () => {
  const user = userService.getUser(userId);
  // ...
};
```

#### 3. Inconsistent Naming
```typescript
// ❌ Inconsistent
import { getUserData } from '@/auth';
import { fetchUserPermissions } from '@/rbac';

// ✅ Consistent
import { getUser } from '@/auth';
import { getUserPermissions } from '@/rbac';
```

### 5. Migration Strategy

#### Gradual Migration
1. Start with new features using the modular structure
2. Extract existing features one module at a time
3. Update imports gradually
4. Remove old code once fully migrated

#### Migration Checklist
- [ ] Create module directory structure
- [ ] Move related files to module
- [ ] Update imports throughout codebase
- [ ] Add tests for module
- [ ] Update documentation
- [ ] Remove old files

## Conclusion

This modular architecture provides a solid foundation for building scalable Next.js applications with OpenNext. By following these patterns and conventions, AI agents can consistently create well-organized, maintainable, and testable code that integrates seamlessly with the Next.js App Router and deploys efficiently with OpenNext.

Remember to:
- Keep modules focused and single-purpose
- Maintain clear boundaries between modules
- Use TypeScript for better developer experience
- Write comprehensive tests
- Document your modules thoroughly
- Consider OpenNext deployment constraints

This architecture will grow with your application while maintaining code quality and developer productivity.
