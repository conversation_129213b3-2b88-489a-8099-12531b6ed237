// Application configuration for modules and plugins

import type { AppConfig } from './core/system/types';

export const config: AppConfig = {
  // Global configuration
  global: {
    apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
    debug: process.env.NODE_ENV === 'development',
    environment: process.env.NODE_ENV || 'development',
  },

  // Module configuration
  modules: {
    // Core modules
    auth: {
      enabled: true,
      config: {
        providers: ['credentials', 'google'],
        sessionDuration: 24 * 60 * 60 * 1000, // 24 hours
        cookieName: 'auth-session',
        redirects: {
          login: '/login',
          logout: '/',
          afterLogin: '/dashboard',
          afterLogout: '/',
        },
      },
    },

    database: {
      enabled: true,
      config: {
        provider: 'postgresql',
        url: process.env.DATABASE_URL,
        migrations: {
          enabled: true,
          directory: './drizzle/migrations',
        },
        logging: process.env.NODE_ENV === 'development',
      },
    },

    firebase: {
      enabled: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID ? true : false,
      config: {
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
        authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
        storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
        messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
        appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
        // Server-side config
        serviceAccountKey: process.env.FIREBASE_SERVICE_ACCOUNT_KEY,
      },
    },

    api: {
      enabled: true,
      config: {
        baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000/api',
        timeout: 10000,
        retries: 3,
        headers: {
          'Content-Type': 'application/json',
        },
      },
    },

    ui: {
      enabled: true,
      config: {
        theme: {
          default: 'light',
          storageKey: 'app-theme',
        },
        components: {
          loadingSpinner: true,
          errorBoundary: true,
          toast: true,
        },
      },
    },

    // Optional modules (disabled by default)
    rbac: false, // Enable when needed
    analytics: false, // Enable when needed
    notifications: false, // Enable when needed
  },

  // Plugin configuration
  plugins: {
    // Development plugins
    devtools: {
      enabled: process.env.NODE_ENV === 'development',
      config: {
        showRegistry: true,
        showEvents: true,
        showPerformance: true,
      },
    },

    // Analytics plugins
    googleAnalytics: {
      enabled: process.env.NEXT_PUBLIC_GA_ID ? true : false,
      config: {
        trackingId: process.env.NEXT_PUBLIC_GA_ID,
        anonymizeIp: true,
        respectDnt: true,
      },
    },

    // Error tracking
    sentry: {
      enabled: process.env.NEXT_PUBLIC_SENTRY_DSN ? true : false,
      config: {
        dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
        environment: process.env.NODE_ENV,
        tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
      },
    },

    // Performance monitoring
    webVitals: {
      enabled: true,
      config: {
        reportToAnalytics: true,
        thresholds: {
          fcp: 2000,
          lcp: 2500,
          fid: 100,
          cls: 0.1,
        },
      },
    },

    // SEO optimization
    seo: {
      enabled: true,
      config: {
        defaultTitle: 'My Next.js App',
        titleTemplate: '%s | My Next.js App',
        defaultDescription: 'A modern Next.js application',
        openGraph: {
          type: 'website',
          locale: 'en_US',
          siteName: 'My Next.js App',
        },
        twitter: {
          cardType: 'summary_large_image',
        },
      },
    },

    // PWA support
    pwa: {
      enabled: process.env.NODE_ENV === 'production',
      config: {
        name: 'My Next.js App',
        shortName: 'NextApp',
        description: 'A modern Next.js application',
        themeColor: '#000000',
        backgroundColor: '#ffffff',
        display: 'standalone',
        orientation: 'portrait',
      },
    },

    // Security headers
    security: {
      enabled: process.env.NODE_ENV === 'production',
      config: {
        contentSecurityPolicy: true,
        frameOptions: 'DENY',
        contentTypeOptions: true,
        referrerPolicy: 'strict-origin-when-cross-origin',
        hsts: {
          maxAge: 31536000,
          includeSubDomains: true,
          preload: true,
        },
      },
    },
  },
};

export default config;
