{"compilerOptions": {"target": "ES2017", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "paths": {"@/*": ["./*"], "@/core/*": ["./core/*"], "@/modules/*": ["./core/modules/*"], "@plugins/*": ["./core/plugins/*"], "@system": ["./core/system"], "@/auth": ["./core/modules/auth"], "@/rbac": ["./core/modules/rbac"], "@/database": ["./core/modules/database"], "@/firebase": ["./core/modules/firebase"], "@/ui": ["./core/modules/ui"], "@/api": ["./core/modules/api"]}, "types": ["./cloudflare-env.d.ts", "node"]}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}